import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';

/// Welcome/Onboarding screen with introduction and navigation options
class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 100));
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.spacingL),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Logo section
                          Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              color: isDark
                                  ? AppConstants.secondaryBlue.withOpacity(0.1)
                                  : AppConstants.lightBlue,
                              borderRadius: BorderRadius.circular(
                                AppConstants.radiusXL,
                              ),
                              border: Border.all(
                                color: AppConstants.primaryBlue.withOpacity(
                                  0.2,
                                ),
                                width: 2,
                              ),
                            ),
                            child: Icon(
                              Icons.receipt_long,
                              size: 80,
                              color: isDark
                                  ? AppConstants.secondaryBlue
                                  : AppConstants.primaryBlue,
                            ),
                          ),

                          const SizedBox(height: AppConstants.spacingXL),

                          // Welcome title
                          Text(
                            AppConstants.welcomeTitle,
                            style: Theme.of(context).textTheme.displayLarge,
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: AppConstants.spacingM),

                          // Subtitle
                          Text(
                            AppConstants.welcomeSubtitle,
                            style: Theme.of(context).textTheme.displayMedium
                                ?.copyWith(
                                  color: AppConstants.primaryBlue,
                                  fontWeight: FontWeight.w500,
                                ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: AppConstants.spacingXL),

                          // Description
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppConstants.spacingM,
                            ),
                            child: Text(
                              AppConstants.welcomeDescription,
                              style: Theme.of(
                                context,
                              ).textTheme.bodyLarge?.copyWith(height: 1.6),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          const SizedBox(height: AppConstants.spacingXXL),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom buttons
              FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // Get Started button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          context.go('/signup');
                        },
                        child: const Text(AppConstants.getStartedButton),
                      ),
                    ),

                    const SizedBox(height: AppConstants.spacingM),

                    // Sign In button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          context.go('/signin');
                        },
                        child: const Text(AppConstants.signInButton),
                      ),
                    ),

                    const SizedBox(height: AppConstants.spacingL),

                    // Terms and privacy text
                    Text(
                      'By continuing, you agree to our Terms of Service and Privacy Policy',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: AppConstants.textSizeXS,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
