import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_constants.dart';

/// App theme configuration for SIRA
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();

  /// Light theme configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // Color Scheme - Pink/Magenta Theme
      colorScheme: const ColorScheme.light(
        primary: AppConstants.primaryPink,
        onPrimary: AppConstants.white,
        secondary: AppConstants.secondaryPink,
        onSecondary: AppConstants.white,
        surface: AppConstants.white,
        onSurface: AppConstants.textPrimary,
        error: AppConstants.errorRed,
        onError: AppConstants.white,
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppConstants.primaryPink,
        foregroundColor: AppConstants.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.inter(
          fontSize: AppConstants.textSizeL,
          fontWeight: FontWeight.w600,
          color: AppConstants.white,
        ),
      ),

      // Text Theme
      textTheme: TextTheme(
        displayLarge: GoogleFonts.inter(
          fontSize: AppConstants.textSizeXXL,
          fontWeight: FontWeight.bold,
          color: AppConstants.darkGray,
        ),
        displayMedium: GoogleFonts.inter(
          fontSize: AppConstants.textSizeXL,
          fontWeight: FontWeight.w600,
          color: AppConstants.darkGray,
        ),
        headlineLarge: GoogleFonts.inter(
          fontSize: AppConstants.textSizeL,
          fontWeight: FontWeight.w600,
          color: AppConstants.darkGray,
        ),
        bodyLarge: GoogleFonts.inter(
          fontSize: AppConstants.textSizeM,
          fontWeight: FontWeight.normal,
          color: AppConstants.darkGray,
        ),
        bodyMedium: GoogleFonts.inter(
          fontSize: AppConstants.textSizeS,
          fontWeight: FontWeight.normal,
          color: AppConstants.mediumGray,
        ),
        labelLarge: GoogleFonts.inter(
          fontSize: AppConstants.textSizeM,
          fontWeight: FontWeight.w500,
          color: AppConstants.white,
        ),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryPink,
          foregroundColor: AppConstants.white,
          minimumSize: const Size(double.infinity, AppConstants.buttonHeightM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: AppConstants.textSizeM,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppConstants.primaryPink,
          minimumSize: const Size(double.infinity, AppConstants.buttonHeightM),
          side: const BorderSide(color: AppConstants.primaryPink, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: AppConstants.textSizeM,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppConstants.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusM),
          borderSide: const BorderSide(color: AppConstants.mediumGray),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusM),
          borderSide: const BorderSide(color: AppConstants.mediumGray),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusM),
          borderSide: const BorderSide(
            color: AppConstants.primaryPink,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusM),
          borderSide: const BorderSide(color: AppConstants.errorRed),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.spacingM,
          vertical: AppConstants.spacingM,
        ),
      ),
    );
  }
}
