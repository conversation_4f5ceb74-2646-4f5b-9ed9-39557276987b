import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../core/providers/base_auth_provider.dart';
import '../../../core/models/subscription_model.dart';
import '../../../core/config/subscription_config.dart';
import '../../widgets/subscription/subscription_plan_card.dart';
import '../../widgets/common/loading_button.dart';

/// Subscription screen for selecting and purchasing plans
class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  SubscriptionType? _selectedPlan;
  bool _isInitialized = false;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _initializeSubscription();
  }

  Future<void> _initializeSubscription() async {
    final authProvider = context.read<BaseAuthProvider>();
    final subscriptionProvider = context.read<SubscriptionProvider>();

    if (authProvider.user != null) {
      await subscriptionProvider.initialize(authProvider.user!.uid);

      // Don't redirect users with active subscriptions - let them manage their subscription

      // Set default selection to trial if available
      final canStartTrial = await subscriptionProvider.canStartTrial(
        authProvider.user!.uid,
      );
      if (canStartTrial) {
        setState(() {
          _selectedPlan = SubscriptionType.trial;
        });
      } else {
        setState(() {
          _selectedPlan = SubscriptionType.monthly;
        });
      }
    }

    setState(() {
      _isInitialized = true;
    });
  }

  Future<void> _handleSubscription() async {
    if (_selectedPlan == null) return;

    final authProvider = context.read<BaseAuthProvider>();
    final subscriptionProvider = context.read<SubscriptionProvider>();

    if (authProvider.user == null) {
      _showErrorSnackBar('Please sign in to continue');
      return;
    }

    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog();
    if (!confirmed || !mounted) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final success = await subscriptionProvider.purchaseSubscription(
        context: context,
        userId: authProvider.user!.uid,
        subscriptionType: _selectedPlan!,
      );

      if (success && mounted) {
        await _showSuccessDialog();
        if (mounted) {
          context.go('/home');
        }
      } else if (mounted) {
        final error =
            subscriptionProvider.error ?? 'Failed to activate subscription';
        _showErrorSnackBar(error);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<bool> _showConfirmationDialog() async {
    if (_selectedPlan == null) return false;

    final plan = SubscriptionConfig.getPlanByType(_selectedPlan!);
    if (plan == null) return false;

    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Confirm Subscription'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('You are about to subscribe to:'),
                const SizedBox(height: AppConstants.spacingM),
                Container(
                  padding: const EdgeInsets.all(AppConstants.spacingM),
                  decoration: BoxDecoration(
                    color: AppConstants.lightPink,
                    borderRadius: BorderRadius.circular(AppConstants.radiusM),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        plan.title,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppConstants.primaryPink,
                            ),
                      ),
                      const SizedBox(height: AppConstants.spacingS),
                      Text(plan.subtitle),
                      const SizedBox(height: AppConstants.spacingS),
                      Text(
                        plan.formattedPrice,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppConstants.primaryPink,
                            ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.spacingM),
                if (plan.type != SubscriptionType.trial)
                  Text(
                    'You will be charged ${plan.formattedPrice} ${plan.duration}.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.mediumGray,
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryPink,
                ),
                child: Text(
                  plan.type == SubscriptionType.trial
                      ? 'Start Trial'
                      : 'Subscribe',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> _showSuccessDialog() async {
    if (_selectedPlan == null) return;

    final plan = SubscriptionConfig.getPlanByType(_selectedPlan!);
    if (plan == null) return;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: AppConstants.successGreen,
              size: 28,
            ),
            const SizedBox(width: AppConstants.spacingS),
            Text('Success!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              plan.type == SubscriptionType.trial
                  ? 'Your free trial has started!'
                  : 'Subscription activated successfully!',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: AppConstants.spacingM),
            Container(
              padding: const EdgeInsets.all(AppConstants.spacingM),
              decoration: BoxDecoration(
                color: AppConstants.lightPink,
                borderRadius: BorderRadius.circular(AppConstants.radiusM),
              ),
              child: Column(
                children: [
                  Text(
                    plan.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppConstants.primaryPink,
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacingS),
                  Text(
                    plan.type == SubscriptionType.trial
                        ? 'Enjoy full access for ${plan.duration}'
                        : 'You now have full access to all features',
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryPink,
            ),
            child: Text(
              'Continue to App',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppConstants.errorRed),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Subscription'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: !_isInitialized
          ? const Center(child: CircularProgressIndicator())
          : Consumer<SubscriptionProvider>(
              builder: (context, subscriptionProvider, child) {
                if (subscriptionProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                // Show different content based on subscription status
                if (subscriptionProvider.hasActiveSubscription) {
                  return _buildSubscriptionManagement(subscriptionProvider);
                } else {
                  return _buildSubscriptionPlans(subscriptionProvider);
                }
              },
            ),
    );
  }

  Widget _buildSubscriptionPlans(SubscriptionProvider subscriptionProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Unlock Full Access',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.spacingS),
          Text(
            'Choose the perfect plan for your needs. Start with a free trial or select a subscription plan.',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: AppConstants.spacingXL),

          // Subscription Plans
          ...SubscriptionConfig.availablePlans.map((plan) {
            return Padding(
              padding: const EdgeInsets.only(bottom: AppConstants.spacingL),
              child: SubscriptionPlanCard(
                plan: plan,
                isSelected: _selectedPlan == plan.type,
                onTap: () {
                  setState(() {
                    _selectedPlan = plan.type;
                  });

                  // Show immediate feedback
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${plan.title} selected'),
                      duration: const Duration(seconds: 1),
                      backgroundColor: AppConstants.primaryPink,
                    ),
                  );
                },
              ),
            );
          }),

          const SizedBox(height: AppConstants.spacingXL),

          // Selection status
          if (_selectedPlan != null) ...[
            Container(
              padding: const EdgeInsets.all(AppConstants.spacingM),
              decoration: BoxDecoration(
                color: AppConstants.lightPink,
                borderRadius: BorderRadius.circular(AppConstants.radiusM),
                border: Border.all(
                  color: AppConstants.primaryPink.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppConstants.primaryPink,
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.spacingS),
                  Expanded(
                    child: Text(
                      'Selected: ${SubscriptionConfig.getPlanByType(_selectedPlan!)?.title ?? 'Unknown Plan'}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppConstants.primaryPink,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppConstants.spacingL),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(AppConstants.spacingM),
              decoration: BoxDecoration(
                color: AppConstants.mediumGray.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusM),
                border: Border.all(
                  color: AppConstants.mediumGray.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppConstants.mediumGray,
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.spacingS),
                  Expanded(
                    child: Text(
                      'Please select a subscription plan above',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppConstants.mediumGray,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppConstants.spacingL),
          ],

          // Subscribe Button
          SizedBox(
            width: double.infinity,
            child: LoadingButton(
              onPressed: _selectedPlan != null && !_isProcessing
                  ? _handleSubscription
                  : null,
              isLoading: subscriptionProvider.isLoading || _isProcessing,
              child: Text(
                _isProcessing ? 'Processing...' : _getButtonText(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          const SizedBox(height: AppConstants.spacingL),

          // Terms and conditions
          Text(
            'By subscribing, you agree to our Terms of Service and Privacy Policy. '
            'Subscriptions will automatically renew unless cancelled.',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.spacingL),

          // Skip for now (if trial is not available)
          Consumer<BaseAuthProvider>(
            builder: (context, authProvider, child) {
              return FutureBuilder<bool>(
                future: authProvider.user != null
                    ? subscriptionProvider.canStartTrial(authProvider.user!.uid)
                    : Future.value(false),
                builder: (context, snapshot) {
                  final canStartTrial = snapshot.data ?? false;

                  if (!canStartTrial) {
                    return Center(
                      child: TextButton(
                        onPressed: () {
                          // For now, just go back
                          context.go('/welcome');
                        },
                        child: const Text('Maybe Later'),
                      ),
                    );
                  }

                  return const SizedBox.shrink();
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionManagement(
    SubscriptionProvider subscriptionProvider,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Manage Subscription',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.spacingS),
          Text(
            'View and manage your current subscription details.',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: AppConstants.spacingXL),

          // Current Subscription Card
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppConstants.successGreen,
                        size: 24,
                      ),
                      const SizedBox(width: AppConstants.spacingS),
                      Text(
                        'Active Subscription',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.spacingL),

                  _buildInfoRow('Status', subscriptionProvider.statusText),

                  if (subscriptionProvider.currentSubscriptionType != null)
                    _buildInfoRow(
                      'Plan',
                      subscriptionProvider.currentSubscriptionType!.displayName,
                    ),

                  if (subscriptionProvider.nextBillingDate != null)
                    _buildInfoRow(
                      'Next Billing',
                      _formatDate(subscriptionProvider.nextBillingDate!),
                    ),

                  _buildInfoRow(
                    'Remaining Days',
                    '${subscriptionProvider.remainingDays} days',
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppConstants.spacingXL),

          // Management Options
          Text(
            'Subscription Options',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.spacingM),

          // Upgrade/Change Plan Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // Show available plans for upgrade
                _showUpgradeOptions(subscriptionProvider);
              },
              icon: const Icon(Icons.upgrade),
              label: const Text('Change Plan'),
            ),
          ),

          const SizedBox(height: AppConstants.spacingM),

          // Cancel Subscription Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                _showCancelConfirmation(subscriptionProvider);
              },
              icon: const Icon(Icons.cancel),
              label: const Text('Cancel Subscription'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppConstants.errorRed,
                side: BorderSide(color: AppConstants.errorRed),
              ),
            ),
          ),

          const SizedBox(height: AppConstants.spacingXL),

          // Back to Home Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Back to Home'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacingS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showUpgradeOptions(SubscriptionProvider subscriptionProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(AppConstants.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Upgrade Your Plan',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.spacingL),
            Expanded(
              child: ListView(
                children: SubscriptionConfig.availablePlans
                    .where(
                      (plan) =>
                          plan.type !=
                          subscriptionProvider.currentSubscriptionType,
                    )
                    .map(
                      (plan) => Card(
                        child: ListTile(
                          title: Text(plan.title),
                          subtitle: Text(plan.subtitle),
                          trailing: Text(
                            plan.formattedPrice,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppConstants.primaryPink,
                                ),
                          ),
                          onTap: () {
                            Navigator.pop(context);
                            // Handle plan change
                            _handlePlanChange(plan.type);
                          },
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelConfirmation(SubscriptionProvider subscriptionProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Subscription'),
        content: const Text(
          'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Keep Subscription'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _handleCancelSubscription(subscriptionProvider);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorRed,
            ),
            child: const Text('Cancel Subscription'),
          ),
        ],
      ),
    );
  }

  void _handlePlanChange(SubscriptionType newPlan) async {
    final authProvider = context.read<BaseAuthProvider>();
    final subscriptionProvider = context.read<SubscriptionProvider>();

    if (authProvider.user == null) {
      _showErrorSnackBar('User not authenticated');
      return;
    }

    // Show confirmation dialog
    final confirmed = await _showPlanChangeConfirmation(newPlan);
    if (!confirmed) return;

    try {
      final success = await subscriptionProvider.changeSubscriptionPlan(
        context: context,
        userId: authProvider.user!.uid,
        newPlan: newPlan,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully changed to ${newPlan.displayName}'),
            backgroundColor: AppConstants.successGreen,
          ),
        );
      } else if (mounted) {
        final error = subscriptionProvider.error ?? 'Failed to change plan';
        _showErrorSnackBar(error);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error changing plan: ${e.toString()}');
      }
    }
  }

  Future<bool> _showPlanChangeConfirmation(SubscriptionType newPlan) async {
    final plan = SubscriptionConfig.getPlanByType(newPlan);
    if (plan == null) return false;

    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Change Subscription Plan'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Are you sure you want to change to:'),
                const SizedBox(height: AppConstants.spacingM),
                Text(
                  plan.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryPink,
                  ),
                ),
                Text(
                  '${plan.formattedPrice} ${plan.duration}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.spacingM),
                if (newPlan != SubscriptionType.trial)
                  Text(
                    'You will be charged immediately for the new plan.',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.orange[700]),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryPink,
                ),
                child: Text(
                  newPlan == SubscriptionType.trial
                      ? 'Switch to Trial'
                      : 'Change Plan',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _handleCancelSubscription(
    SubscriptionProvider subscriptionProvider,
  ) async {
    try {
      final success = await subscriptionProvider.cancelSubscription();

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Subscription cancelled successfully'),
            backgroundColor: AppConstants.successGreen,
          ),
        );
        // Navigate back to home after cancellation
        context.go('/home');
      } else if (mounted) {
        final error =
            subscriptionProvider.error ?? 'Failed to cancel subscription';
        _showErrorSnackBar(error);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error cancelling subscription: ${e.toString()}');
      }
    }
  }

  String _getButtonText() {
    if (_selectedPlan == null) return 'Select a Plan';

    final plan = SubscriptionConfig.getPlanByType(_selectedPlan!);
    return plan?.buttonText ?? 'Subscribe';
  }
}
