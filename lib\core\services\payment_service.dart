import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import '../models/subscription_model.dart';
import '../config/subscription_config.dart';

/// Service for handling subscription payments using Stripe
class PaymentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Process subscription payment using Stripe
  Future<PaymentResult> processPayment({
    required BuildContext context,
    required SubscriptionType subscriptionType,
    required String userId,
  }) async {
    try {
      final plan = SubscriptionConfig.getPlanByType(subscriptionType);
      if (plan == null) {
        return PaymentResult.error('Invalid subscription plan');
      }

      if (subscriptionType == SubscriptionType.trial) {
        return PaymentResult.error(
          'Trial subscriptions do not require payment',
        );
      }

      // Create a checkout session in Firestore to trigger the extension
      final sessionData = {
        'userId': userId,
        'priceId': _getPriceIdForPlan(subscriptionType),
        'success_url': 'https://yourdomain.com/success',
        'cancel_url': 'https://yourdomain.com/cancel',
        'created': FieldValue.serverTimestamp(),
      };

      // Add document to the collection that triggers the extension
      final docRef = await _firestore
          .collection('stripe_customers')
          .doc(userId)
          .collection('checkout_sessions')
          .add(sessionData);

      // Wait for the extension to update the document with the session
      DocumentSnapshot snapshot;
      int attempts = 0;
      const maxAttempts = 30; // 30 seconds timeout

      do {
        await Future.delayed(const Duration(seconds: 1));
        snapshot = await docRef.get();
        attempts++;

        if (attempts >= maxAttempts) {
          return PaymentResult.error('Timeout waiting for payment session');
        }
      } while (!snapshot.exists ||
          !(snapshot.data() as Map<String, dynamic>).containsKey('sessionId'));

      final data = snapshot.data() as Map<String, dynamic>;
      final sessionId = data['sessionId'];

      // Check if there's an error from the extension
      if (data.containsKey('error')) {
        return PaymentResult.error('Payment session error: ${data['error']}');
      }

      // Present the payment sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: data['client_secret'],
          merchantDisplayName: 'Smart Invoice-Receipt Assistant',
        ),
      );

      await Stripe.instance.presentPaymentSheet();

      return PaymentResult.success(
        orderId: sessionId,
        subscriptionId: null, // Will be updated after webhook confirmation
        details: {
          'status': 'success',
          'amount': plan.price,
          'currency': SubscriptionConfig.defaultCurrency,
          'sessionId': sessionId,
        },
      );
    } catch (e) {
      debugPrint('Error processing subscription: $e');
      return PaymentResult.error(
        'Subscription processing failed: ${e.toString()}',
      );
    }
  }

  /// Get Stripe price ID for subscription plan
  String _getPriceIdForPlan(SubscriptionType type) {
    switch (type) {
      case SubscriptionType.monthly:
        return SubscriptionConfig.monthlyPriceId;
      case SubscriptionType.yearly:
        return SubscriptionConfig.yearlyPriceId;
      default:
        throw Exception('No price ID for this subscription type');
    }
  }

  /// Cancel subscription (simplified - no external cancellation needed)
  Future<bool> cancelSubscription(String? subscriptionId) async {
    try {
      // Since we're not using external payment providers,
      // cancellation is handled internally by the subscription service
      debugPrint('Cancelling subscription: $subscriptionId');
      return true;
    } catch (e) {
      debugPrint('Error cancelling subscription: $e');
      return false;
    }
  }

  /// Format currency amount for display
  String formatCurrency(double amount, String currency) {
    switch (currency.toUpperCase()) {
      case 'USD':
        return '\$${amount.toStringAsFixed(2)}';
      case 'EUR':
        return '€${amount.toStringAsFixed(2)}';
      case 'GBP':
        return '£${amount.toStringAsFixed(2)}';
      default:
        return '$currency ${amount.toStringAsFixed(2)}';
    }
  }

  /// Calculate tax amount (if applicable)
  double calculateTax(double amount, String region) {
    // In a production app, you would calculate tax based on user's location
    // For now, we'll return 0 (no tax)
    return 0.0;
  }

  /// Get total amount including tax
  double getTotalAmount(double baseAmount, String region) {
    final tax = calculateTax(baseAmount, region);
    return baseAmount + tax;
  }

  /// Validate payment amount
  bool validateAmount(double amount, SubscriptionType type) {
    final plan = SubscriptionConfig.getPlanByType(type);
    if (plan == null) return false;

    // Allow small variance for currency conversion
    const tolerance = 0.01;
    return (amount - plan.price).abs() <= tolerance;
  }
}
