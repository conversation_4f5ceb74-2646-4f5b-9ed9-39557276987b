import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'base_auth_provider.dart';

/// Mock Authentication provider for development without Firebase
/// This allows the app to build and run without Firebase configuration
class MockAuthProvider extends BaseAuthProvider {
  SiraUser? _user;
  bool _isLoading = false;
  String? _error;

  /// Current user
  @override
  SiraUser? get user => _user;

  /// Loading state
  @override
  bool get isLoading => _isLoading;

  /// Error message
  @override
  String? get error => _error;

  /// Whether user is authenticated
  @override
  bool get isAuthenticated => _user != null;

  /// Initialize mock auth provider
  @override
  void initialize() {
    // Mock initialization - no Firebase needed
    debugPrint('Mock AuthProvider initialized');
  }

  /// Mock sign in
  @override
  Future<bool> signIn({required String email, required String password}) async {
    _setLoading(true);
    _clearError();

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock validation
    if (email.isEmpty || password.isEmpty) {
      _setError('Please fill in all fields');
      _setLoading(false);
      return false;
    }

    if (!email.contains('@')) {
      _setError('Please enter a valid email address');
      _setLoading(false);
      return false;
    }

    if (password.length < 6) {
      _setError('Password must be at least 6 characters');
      _setLoading(false);
      return false;
    }

    // Mock successful sign in
    _user = SiraUser(
      uid: 'mock_user_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      displayName: email.split('@')[0],
      emailVerified: true,
      createdAt: DateTime.now(),
      lastSignIn: DateTime.now(),
    );

    _setLoading(false);
    return true;
  }

  /// Mock Google sign in
  @override
  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    _clearError();

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock successful Google sign in
    _user = SiraUser(
      uid: 'mock_google_user_${DateTime.now().millisecondsSinceEpoch}',
      email: '<EMAIL>',
      displayName: 'Mock Google User',
      emailVerified: true,
      createdAt: DateTime.now(),
      lastSignIn: DateTime.now(),
    );

    _setLoading(false);
    return true;
  }

  /// Mock sign up
  @override
  Future<bool> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    _setLoading(true);
    _clearError();

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock validation
    if (email.isEmpty || password.isEmpty) {
      _setError('Please fill in all fields');
      _setLoading(false);
      return false;
    }

    if (!email.contains('@')) {
      _setError('Please enter a valid email address');
      _setLoading(false);
      return false;
    }

    if (password.length < 6) {
      _setError('Password must be at least 6 characters');
      _setLoading(false);
      return false;
    }

    // Mock successful sign up
    _user = SiraUser(
      uid: 'mock_user_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      displayName: displayName?.isNotEmpty == true
          ? displayName
          : email.split('@')[0],
      emailVerified: false, // New accounts need verification
      createdAt: DateTime.now(),
      lastSignIn: DateTime.now(),
    );

    _setLoading(false);
    return true;
  }

  /// Mock password reset
  @override
  Future<bool> sendPasswordResetEmail({required String email}) async {
    _setLoading(true);
    _clearError();

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    if (email.isEmpty || !email.contains('@')) {
      _setError('Please enter a valid email address');
      _setLoading(false);
      return false;
    }

    // Mock successful password reset
    _setLoading(false);
    return true;
  }

  /// Mock email verification
  @override
  Future<bool> sendEmailVerification() async {
    _setLoading(true);
    _clearError();

    await Future.delayed(const Duration(seconds: 1));

    if (_user != null) {
      _user = _user!.copyWith(emailVerified: true);
      _setLoading(false);
      return true;
    }

    _setError('No user found');
    _setLoading(false);
    return false;
  }

  /// Mock password update
  @override
  Future<bool> updatePassword({required String newPassword}) async {
    _setLoading(true);
    _clearError();

    await Future.delayed(const Duration(seconds: 1));

    if (newPassword.length < 6) {
      _setError('Password must be at least 6 characters');
      _setLoading(false);
      return false;
    }

    _setLoading(false);
    return true;
  }

  /// Mock sign out
  @override
  Future<void> signOut() async {
    _setLoading(true);
    await Future.delayed(const Duration(milliseconds: 500));
    _user = null;
    _setLoading(false);
  }

  /// Clear error message
  @override
  void clearError() {
    _clearError();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
