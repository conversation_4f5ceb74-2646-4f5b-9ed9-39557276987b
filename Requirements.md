Product Requirements Document (PRD)
## Project Title:  Smart Invoice-Receipt Assistant (sira)
a. Overview
This project is a mobile application built using Flutter, designed to act as a secure and user-friendly gateway to an external web service. The application will include splash screens, user onboarding, Firebase authentication, subscription billing, and a user profile interface. Access to the core functionality (via external URL) is restricted based on subscription status.

b. Goals & Objectives
•	Deliver a smooth onboarding and login experience via Firebase Auth.
•	Provide users with tiered subscription options, including a 72-hour free trial.
•	Allow access to a secure external URL only for subscribed users.
•	Implement PayPal for billing and managing subscriptions.
•	Provide user profile settings with password management.
•	Ensure app scalability and maintainability using the Firebase backend.

c. Target Platforms
•	Android
•	iOS

## Cole Functionalities
1. Splash screen & First-time Onboarding 
•	Branded Animated splash screen using flutter_native_splash.
•	Get Started / Welcome page with a brief introduction and navigation to sign in or sign up.

2. Authentication (via Firebase)
•	Email & Password login/signup
•	Password reset functionality
•	Firebase session management
•	Error handling for invalid credentials
3. Subscription Flow
•	Post-login, the user is directed to the Subscription Page
•	Payment via PayPal SDKflutt
•	3 Pricing Options:
o	Free Trial: 72 hours (auto-expire access)
o	Monthly: $3.99
o	Yearly: $34.99
•	Firebase stores subscription details and access flags
•	Logic to control access based on subscription status

4. Home Page & Main Feature Access
•	Display welcome text
•	Access button to open the external URL in WebView or an external browser
•	Button is only active if the user has a valid subscription or trial

5. Profile Page
•	Display user details (Email - view only, Password - changeable)
•	Button to initiate password update
•	Logout functionality

6. Other features

-  Dark mode and white mode options
-  Notifications for subscription expiration

