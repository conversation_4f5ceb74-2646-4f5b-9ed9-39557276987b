@echo off
echo Getting SHA-1 key for Firebase...
echo.

REM Try different Java paths
set JAVA_PATHS[0]="C:\Program Files\Java\jdk-11\bin\keytool.exe"
set JAVA_PATHS[1]="C:\Program Files\Java\jdk-17\bin\keytool.exe"
set JAVA_PATHS[2]="C:\Program Files\Java\jdk-21\bin\keytool.exe"
set JAVA_PATHS[3]="C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe"
set JAVA_PATHS[4]="C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\keytool.exe"

for /L %%i in (0,1,4) do (
    call set "KEYTOOL_PATH=%%JAVA_PATHS[%%i]%%"
    if exist !KEYTOOL_PATH! (
        echo Found keytool at: !KEYTOOL_PATH!
        echo.
        echo SHA-1 Key:
        !KEYTOOL_PATH! -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android | findstr SHA1
        echo.
        echo Copy the SHA1 value above and add it to your Firebase project settings.
        echo Go to: Firebase Console > Project Settings > Your Apps > Add fingerprint
        pause
        exit /b 0
    )
)

echo Could not find keytool. Please install Java JDK or Android Studio.
echo.
echo Manual instructions:
echo 1. Find your Java installation directory
echo 2. Navigate to the bin folder
echo 3. Run this command:
echo    keytool -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android
echo 4. Look for the SHA1 line in the output
echo.
pause
