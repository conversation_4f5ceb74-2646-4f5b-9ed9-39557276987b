// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:sira_pro/core/theme/app_theme.dart';

void main() {
  testWidgets('App theme and constants work correctly', (
    WidgetTester tester,
  ) async {
    // Build a simple widget using our theme
    await tester.pumpWidget(
      MaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('SIRA', style: const TextStyle(fontSize: 24)),
                Text(
                  'Smart Invoice-Receipt Assistant',
                  style: const TextStyle(fontSize: 16),
                ),
                const Icon(Icons.receipt_long),
              ],
            ),
          ),
        ),
      ),
    );

    // Verify that the app constants are working
    expect(find.text('SIRA'), findsOneWidget);
    expect(find.text('Smart Invoice-Receipt Assistant'), findsOneWidget);

    // Verify the icon is present
    expect(find.byIcon(Icons.receipt_long), findsOneWidget);
  });
}
