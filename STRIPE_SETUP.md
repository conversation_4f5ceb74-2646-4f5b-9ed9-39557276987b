# Stripe Payment Integration Setup

This document outlines the steps to complete the Stripe payment integration for the SIRA application.

## Prerequisites

1. **Stripe Account**: Create a Stripe account at [https://stripe.com](https://stripe.com)
2. **Firebase Project**: Ensure your Firebase project is set up and configured
3. **Firebase CLI**: Install and configure Firebase CLI

## Setup Steps

### 1. Install Firebase Stripe Extension

```bash
# Navigate to your project directory
cd your-project-directory

# Install the Stripe extension
firebase ext:install firestore-stripe-subscriptions --project=your-project-id
```

During installation, you'll be prompted to configure:
- **Stripe webhook endpoint secret**: Get this from your Stripe dashboard
- **Stripe API key**: Your Stripe secret key (starts with `sk_`)
- **Products and pricing**: Configure your subscription products

### 2. Create Stripe Products and Prices

In your Stripe Dashboard:

1. Go to **Products** → **Add Product**
2. Create two products:
   - **Monthly Subscription**: $3.99/month
   - **Yearly Subscription**: $34.99/year

3. Copy the Price IDs (they start with `price_`) for each product

### 3. Update Configuration

Update `lib/core/config/subscription_config.dart`:

```dart
// Stripe configuration
static const String publishableKey = 'pk_test_your_actual_publishable_key';
static const String monthlyPriceId = 'price_your_actual_monthly_price_id';
static const String yearlyPriceId = 'price_your_actual_yearly_price_id';
```

### 4. Configure Firestore Security Rules

Add these rules to your Firestore security rules:

```javascript
// Allow users to read/write their own customer data
match /stripe_customers/{uid} {
  allow read, write: if request.auth != null && request.auth.uid == uid;
  
  match /checkout_sessions/{id} {
    allow read, write: if request.auth != null && request.auth.uid == uid;
  }
  
  match /subscriptions/{id} {
    allow read: if request.auth != null && request.auth.uid == uid;
  }
}
```

### 5. Set Up Webhooks

1. In Stripe Dashboard, go to **Developers** → **Webhooks**
2. Add endpoint: `https://your-project-id.cloudfunctions.net/ext-firestore-stripe-subscriptions-handleWebhookEvents`
3. Select events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

### 6. Test the Integration

1. Use Stripe test cards for testing:
   - Success: `************** 4242`
   - Decline: `************** 0002`

2. Monitor the Firebase Functions logs for any issues

## Important Notes

- **Test Mode**: Always test with Stripe test keys first
- **Production**: Replace test keys with live keys for production
- **Security**: Never expose secret keys in client-side code
- **Webhooks**: Ensure webhook endpoints are properly configured for production

## Troubleshooting

### Common Issues

1. **Payment Sheet Not Showing**: Check if Stripe is properly initialized in main.dart
2. **Webhook Errors**: Verify webhook endpoint URL and selected events
3. **Permission Errors**: Check Firestore security rules
4. **Extension Errors**: Check Firebase Functions logs

### Logs to Check

- Firebase Functions logs: `firebase functions:log`
- Stripe webhook logs: Stripe Dashboard → Developers → Webhooks
- Flutter debug console for client-side errors

## Support

For additional help:
- [Stripe Documentation](https://stripe.com/docs)
- [Firebase Extensions Documentation](https://firebase.google.com/docs/extensions)
- [Flutter Stripe Package](https://pub.dev/packages/flutter_stripe)
