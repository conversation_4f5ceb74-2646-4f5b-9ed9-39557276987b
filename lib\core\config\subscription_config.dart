import '../models/subscription_model.dart';

/// Subscription configuration and pricing
class SubscriptionConfig {
  // Pricing configuration
  static const double trialPrice = 0.0;
  static const double monthlyPrice = 3.99;
  static const double yearlyPrice = 34.99;

  // Duration configuration
  static const int trialDurationHours = 72;
  static const int monthlyDurationDays = 30;
  static const int yearlyDurationDays = 365;

  // Currency
  static const String defaultCurrency = 'USD';

  // Stripe configuration
  static const String publishableKey =
      'pk_test_your_publishable_key'; // TODO: Replace with actual publishable key
  static const String monthlyPriceId =
      'price_monthly_id_from_stripe'; // TODO: Replace with actual monthly price ID
  static const String yearlyPriceId =
      'price_yearly_id_from_stripe'; // TODO: Replace with actual yearly price ID

  /// Get all available subscription plans
  static List<SubscriptionPlan> get availablePlans => [
    SubscriptionPlan(
      type: SubscriptionType.trial,
      title: 'Free Trial',
      subtitle: '72 hours full access',
      price: trialPrice,
      originalPrice: null,
      currency: defaultCurrency,
      duration: '$trialDurationHours hours',
      features: [
        'Full access to all features',
        'No credit card required',
        'Automatic expiration after 72 hours',
      ],
      isPopular: false,
      buttonText: 'Start Free Trial',
    ),
    SubscriptionPlan(
      type: SubscriptionType.monthly,
      title: 'Monthly Plan',
      subtitle: 'Perfect for short-term use',
      price: monthlyPrice,
      originalPrice: null,
      currency: defaultCurrency,
      duration: 'per month',
      features: [
        'Full access to all features',
        'Monthly billing',
        'Cancel anytime',
        'Email support',
      ],
      isPopular: false,
      buttonText: 'Subscribe Monthly',
    ),
    SubscriptionPlan(
      type: SubscriptionType.yearly,
      title: 'Yearly Plan',
      subtitle: 'Best value - Save 27%',
      price: yearlyPrice,
      originalPrice: monthlyPrice * 12, // Show savings
      currency: defaultCurrency,
      duration: 'per year',
      features: [
        'Full access to all features',
        'Yearly billing',
        'Save 27% compared to monthly',
        'Priority email support',
        'Early access to new features',
      ],
      isPopular: true,
      buttonText: 'Subscribe Yearly',
    ),
  ];

  /// Get subscription plan by type
  static SubscriptionPlan? getPlanByType(SubscriptionType type) {
    try {
      return availablePlans.firstWhere((plan) => plan.type == type);
    } catch (e) {
      return null;
    }
  }

  /// Calculate end date for subscription
  static DateTime calculateEndDate(SubscriptionType type, DateTime startDate) {
    switch (type) {
      case SubscriptionType.trial:
        return startDate.add(Duration(hours: trialDurationHours));
      case SubscriptionType.monthly:
        return startDate.add(Duration(days: monthlyDurationDays));
      case SubscriptionType.yearly:
        return startDate.add(Duration(days: yearlyDurationDays));
    }
  }

  /// Get trial end date
  static DateTime getTrialEndDate() {
    return DateTime.now().add(const Duration(hours: trialDurationHours));
  }

  /// Check if user can start trial (only once per user)
  static bool canStartTrial(List<Subscription> userSubscriptions) {
    return !userSubscriptions.any((sub) => sub.type == SubscriptionType.trial);
  }

  /// Get savings percentage for yearly plan
  static int getYearlySavingsPercentage() {
    final monthlyTotal = monthlyPrice * 12;
    final savings = monthlyTotal - yearlyPrice;
    return ((savings / monthlyTotal) * 100).round();
  }

  /// Get savings amount for yearly plan
  static double getYearlySavingsAmount() {
    final monthlyTotal = monthlyPrice * 12;
    return monthlyTotal - yearlyPrice;
  }
}

/// Subscription plan model for UI display
class SubscriptionPlan {
  final SubscriptionType type;
  final String title;
  final String subtitle;
  final double price;
  final double? originalPrice;
  final String currency;
  final String duration;
  final List<String> features;
  final bool isPopular;
  final String buttonText;

  const SubscriptionPlan({
    required this.type,
    required this.title,
    required this.subtitle,
    required this.price,
    this.originalPrice,
    required this.currency,
    required this.duration,
    required this.features,
    required this.isPopular,
    required this.buttonText,
  });

  /// Get formatted price string
  String get formattedPrice {
    if (price == 0.0) return 'Free';
    return '\$${price.toStringAsFixed(2)}';
  }

  /// Get formatted original price string
  String? get formattedOriginalPrice {
    if (originalPrice == null) return null;
    return '\$${originalPrice!.toStringAsFixed(2)}';
  }

  /// Get savings text
  String? get savingsText {
    if (originalPrice == null) return null;
    final savings = originalPrice! - price;
    final percentage = ((savings / originalPrice!) * 100).round();
    return 'Save $percentage%';
  }

  /// Get price per month for comparison
  double get pricePerMonth {
    switch (type) {
      case SubscriptionType.trial:
        return 0.0;
      case SubscriptionType.monthly:
        return price;
      case SubscriptionType.yearly:
        return price / 12;
    }
  }

  /// Get formatted price per month
  String get formattedPricePerMonth {
    final monthlyPrice = pricePerMonth;
    if (monthlyPrice == 0.0) return 'Free';
    return '\$${monthlyPrice.toStringAsFixed(2)}/month';
  }
}

/// Subscription limits and quotas
class SubscriptionLimits {
  // Feature access limits (can be extended based on requirements)
  static const Map<SubscriptionType, Map<String, dynamic>> limits = {
    SubscriptionType.trial: {
      'maxDocuments': 50,
      'maxStorageGB': 1,
      'supportLevel': 'basic',
    },
    SubscriptionType.monthly: {
      'maxDocuments': 500,
      'maxStorageGB': 10,
      'supportLevel': 'standard',
    },
    SubscriptionType.yearly: {
      'maxDocuments': -1, // unlimited
      'maxStorageGB': 100,
      'supportLevel': 'premium',
    },
  };

  /// Get limits for subscription type
  static Map<String, dynamic> getLimits(SubscriptionType type) {
    return limits[type] ?? limits[SubscriptionType.trial]!;
  }

  /// Check if feature is available for subscription type
  static bool isFeatureAvailable(SubscriptionType type, String feature) {
    final typeLimits = getLimits(type);
    return typeLimits.containsKey(feature);
  }

  /// Get limit value for specific feature
  static dynamic getLimit(SubscriptionType type, String feature) {
    final typeLimits = getLimits(type);
    return typeLimits[feature];
  }
}
