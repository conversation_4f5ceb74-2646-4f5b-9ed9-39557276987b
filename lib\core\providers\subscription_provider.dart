import 'package:flutter/material.dart';
import '../models/subscription_model.dart';
import '../services/subscription_service.dart';
import '../services/payment_service.dart';
import '../services/notification_service.dart';
import '../config/subscription_config.dart';
import 'dart:async';

/// Subscription state management provider
class SubscriptionProvider extends ChangeNotifier {
  final SubscriptionService _subscriptionService = SubscriptionService();
  final PaymentService _paymentService = PaymentService();
  final NotificationService _notificationService = NotificationService();

  // State variables
  Subscription? _currentSubscription;
  List<Subscription> _subscriptionHistory = [];
  bool _isLoading = false;
  String? _error;
  StreamSubscription<Subscription?>? _subscriptionStreamSubscription;

  // Getters
  Subscription? get currentSubscription => _currentSubscription;
  List<Subscription> get subscriptionHistory => _subscriptionHistory;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Check if user has active subscription
  bool get hasActiveSubscription => _currentSubscription?.isActive ?? false;

  /// Check if user is in trial period
  bool get isInTrial => _currentSubscription?.isInTrial ?? false;

  /// Check if user can access features
  bool get hasAccess => hasActiveSubscription;

  /// Get current subscription type
  SubscriptionType? get currentSubscriptionType => _currentSubscription?.type;

  /// Get remaining days
  int get remainingDays => _currentSubscription?.remainingDays ?? 0;

  /// Get subscription status
  SubscriptionStatus get subscriptionStatus =>
      _currentSubscription?.status ?? SubscriptionStatus.inactive;

  /// Initialize subscription provider for a user
  Future<void> initialize(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      // Initialize notification service
      await _notificationService.initialize();

      // Load current subscription
      await _loadCurrentSubscription(userId);

      // Load subscription history
      await _loadSubscriptionHistory(userId);

      // Set up real-time subscription updates
      _setupSubscriptionStream(userId);

      // Check for expired subscriptions
      await _subscriptionService.checkExpiredSubscriptions();

      // Schedule notifications for current subscription
      await _scheduleNotificationsForCurrentSubscription();
    } catch (e) {
      _setError('Failed to initialize subscription data: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Start free trial
  Future<bool> startFreeTrial(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      // Check if user can start trial
      final canStart = await _subscriptionService.canStartTrial(userId);
      if (!canStart) {
        _setError('You have already used your free trial');
        _setLoading(false);
        return false;
      }

      // Start trial
      final subscription = await _subscriptionService.startFreeTrial(userId);
      if (subscription != null) {
        _currentSubscription = subscription;
        _subscriptionHistory.insert(0, subscription);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to start free trial');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Error starting free trial: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// Purchase subscription (simplified payment flow)
  Future<bool> purchaseSubscription({
    required BuildContext context,
    required String userId,
    required SubscriptionType subscriptionType,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      if (subscriptionType == SubscriptionType.trial) {
        return await startFreeTrial(userId);
      }

      // Process payment
      final paymentResult = await _paymentService.processPayment(
        context: context,
        subscriptionType: subscriptionType,
        userId: userId,
      );

      if (!paymentResult.success) {
        _setError(paymentResult.error ?? 'Payment failed');
        _setLoading(false);
        return false;
      }

      // Create subscription
      final subscription = await _subscriptionService.createPaidSubscription(
        userId: userId,
        type: subscriptionType,
        orderId: paymentResult.orderId!,
      );

      if (subscription != null) {
        // Save payment record
        final plan = SubscriptionConfig.getPlanByType(subscriptionType);
        if (plan != null) {
          await _subscriptionService.savePaymentRecord(
            userId: userId,
            subscriptionId: subscription.id,
            orderId: paymentResult.orderId!,
            amount: plan.price,
            currency: SubscriptionConfig.defaultCurrency,
            paymentDetails: paymentResult.details,
          );
        }

        _currentSubscription = subscription;
        _subscriptionHistory.insert(0, subscription);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to create subscription');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Error purchasing subscription: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// Cancel current subscription
  Future<bool> cancelSubscription() async {
    if (_currentSubscription == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // Cancel subscription (simplified - no external payment provider)
      await _paymentService.cancelSubscription(null);

      // Cancel in our system
      final success = await _subscriptionService.cancelSubscription(
        _currentSubscription!.id,
      );

      if (success) {
        _currentSubscription = _currentSubscription!.copyWith(
          status: SubscriptionStatus.cancelled,
          updatedAt: DateTime.now(),
        );

        // Update in history
        final index = _subscriptionHistory.indexWhere(
          (sub) => sub.id == _currentSubscription!.id,
        );
        if (index != -1) {
          _subscriptionHistory[index] = _currentSubscription!;
        }

        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to cancel subscription');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Error cancelling subscription: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// Change subscription plan
  Future<bool> changeSubscriptionPlan({
    required BuildContext context,
    required String userId,
    required SubscriptionType newPlan,
  }) async {
    if (_currentSubscription == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // If changing to trial, not allowed if user already had trial
      if (newPlan == SubscriptionType.trial) {
        final canStart = await _subscriptionService.canStartTrial(userId);
        if (!canStart) {
          _setError('You have already used your free trial');
          _setLoading(false);
          return false;
        }
      }

      // If changing to a paid plan, process payment
      if (newPlan != SubscriptionType.trial) {
        final paymentResult = await _paymentService.processPayment(
          context: context,
          subscriptionType: newPlan,
          userId: userId,
        );

        if (!paymentResult.success) {
          _setError(paymentResult.error ?? 'Payment failed');
          _setLoading(false);
          return false;
        }

        // Create new subscription with payment details
        final newSubscription = await _subscriptionService
            .createPaidSubscription(
              userId: userId,
              type: newPlan,
              orderId: paymentResult.orderId!,
            );

        if (newSubscription != null) {
          // Save payment record
          final plan = SubscriptionConfig.getPlanByType(newPlan);
          if (plan != null) {
            await _subscriptionService.savePaymentRecord(
              userId: userId,
              subscriptionId: newSubscription.id,
              orderId: paymentResult.orderId!,
              amount: plan.price,
              currency: SubscriptionConfig.defaultCurrency,
              paymentDetails: paymentResult.details,
            );
          }

          _currentSubscription = newSubscription;
          _subscriptionHistory.insert(0, newSubscription);
          _setLoading(false);
          notifyListeners();
          return true;
        }
      } else {
        // Changing to trial - cancel current and start trial
        await _subscriptionService.cancelSubscription(_currentSubscription!.id);

        final trialSubscription = await _subscriptionService.startFreeTrial(
          userId,
        );
        if (trialSubscription != null) {
          _currentSubscription = trialSubscription;
          _subscriptionHistory.insert(0, trialSubscription);
          _setLoading(false);
          notifyListeners();
          return true;
        }
      }

      _setError('Failed to change subscription plan');
      _setLoading(false);
      return false;
    } catch (e) {
      _setError('Error changing subscription plan: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// Refresh subscription data
  Future<void> refresh(String userId) async {
    await initialize(userId);
  }

  /// Check if user can start trial
  Future<bool> canStartTrial(String userId) async {
    return await _subscriptionService.canStartTrial(userId);
  }

  /// Get available subscription plans
  List<SubscriptionPlan> get availablePlans =>
      SubscriptionConfig.availablePlans;

  /// Get plan by type
  SubscriptionPlan? getPlanByType(SubscriptionType type) {
    return SubscriptionConfig.getPlanByType(type);
  }

  /// Clear error message
  void clearError() {
    _clearError();
  }

  /// Dispose resources
  @override
  void dispose() {
    _subscriptionStreamSubscription?.cancel();
    super.dispose();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  Future<void> _loadCurrentSubscription(String userId) async {
    _currentSubscription = await _subscriptionService.getCurrentSubscription(
      userId,
    );
  }

  Future<void> _loadSubscriptionHistory(String userId) async {
    _subscriptionHistory = await _subscriptionService.getUserSubscriptions(
      userId,
    );
  }

  void _setupSubscriptionStream(String userId) {
    _subscriptionStreamSubscription?.cancel();
    _subscriptionStreamSubscription = _subscriptionService
        .subscriptionStream(userId)
        .listen((subscription) {
          if (subscription != _currentSubscription) {
            _currentSubscription = subscription;

            // Update history if needed
            if (subscription != null) {
              final existingIndex = _subscriptionHistory.indexWhere(
                (sub) => sub.id == subscription.id,
              );

              if (existingIndex != -1) {
                _subscriptionHistory[existingIndex] = subscription;
              } else {
                _subscriptionHistory.insert(0, subscription);
              }
            }

            notifyListeners();
          }
        });
  }

  /// Get subscription limits for current subscription
  Map<String, dynamic> get currentLimits {
    if (_currentSubscription == null) {
      return SubscriptionLimits.getLimits(SubscriptionType.trial);
    }
    return SubscriptionLimits.getLimits(_currentSubscription!.type);
  }

  /// Check if feature is available
  bool isFeatureAvailable(String feature) {
    if (_currentSubscription == null || !_currentSubscription!.isActive) {
      return false;
    }
    return SubscriptionLimits.isFeatureAvailable(
      _currentSubscription!.type,
      feature,
    );
  }

  /// Get limit for specific feature
  dynamic getFeatureLimit(String feature) {
    if (_currentSubscription == null) {
      return SubscriptionLimits.getLimit(SubscriptionType.trial, feature);
    }
    return SubscriptionLimits.getLimit(_currentSubscription!.type, feature);
  }

  /// Get formatted subscription status text
  String get statusText {
    if (_currentSubscription == null) {
      return 'No active subscription';
    }

    if (_currentSubscription!.isInTrial) {
      final days = remainingDays;
      final hours =
          (_currentSubscription!.trialEndDate!
              .difference(DateTime.now())
              .inHours %
          24);
      return 'Trial: ${days}d ${hours}h remaining';
    }

    if (_currentSubscription!.isActive) {
      final days = remainingDays;
      return '${_currentSubscription!.type.displayName}: $days days remaining';
    }

    return _currentSubscription!.status.displayName;
  }

  /// Get next billing date
  DateTime? get nextBillingDate {
    if (_currentSubscription == null ||
        _currentSubscription!.type == SubscriptionType.trial) {
      return null;
    }
    return _currentSubscription!.endDate;
  }

  /// Schedule notifications for current subscription
  Future<void> _scheduleNotificationsForCurrentSubscription() async {
    if (_currentSubscription == null || !_currentSubscription!.isActive) {
      return;
    }

    // Get the appropriate expiration date
    DateTime? expirationDate;
    if (_currentSubscription!.type == SubscriptionType.trial) {
      expirationDate = _currentSubscription!.trialEndDate;
    } else {
      expirationDate = _currentSubscription!.endDate;
    }

    if (expirationDate == null) return;

    try {
      await _notificationService.scheduleSubscriptionReminders(
        subscriptionType: _currentSubscription!.type.displayName,
        expirationDate: expirationDate,
      );
      debugPrint(
        'Scheduled notifications for subscription expiring on $expirationDate',
      );
    } catch (e) {
      debugPrint('Error scheduling subscription notifications: $e');
    }
  }

  /// Check and notify about subscription expiration
  Future<void> checkAndNotifyExpiration() async {
    if (_currentSubscription == null) return;

    // Get the appropriate expiration date
    DateTime? expirationDate;
    if (_currentSubscription!.type == SubscriptionType.trial) {
      expirationDate = _currentSubscription!.trialEndDate;
    } else {
      expirationDate = _currentSubscription!.endDate;
    }

    if (expirationDate == null) return;

    final now = DateTime.now();
    final daysUntilExpiration = expirationDate.difference(now).inDays;

    // Show immediate notification if subscription is expiring soon or expired
    if (daysUntilExpiration <= 3) {
      try {
        await _notificationService.showSubscriptionExpirationNotification(
          subscriptionType: _currentSubscription!.type.displayName,
          expirationDate: expirationDate,
        );
      } catch (e) {
        debugPrint('Error showing expiration notification: $e');
      }
    }
  }

  /// Cancel all subscription notifications
  Future<void> cancelSubscriptionNotifications() async {
    try {
      // Cancel subscription-related notifications (IDs 1001-1003)
      await _notificationService.cancelNotification(1001);
      await _notificationService.cancelNotification(1002);
      await _notificationService.cancelNotification(1003);
    } catch (e) {
      debugPrint('Error canceling subscription notifications: $e');
    }
  }
}
