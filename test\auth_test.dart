import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:sira_pro/core/theme/app_theme.dart';
import 'package:sira_pro/presentation/widgets/common/custom_text_field.dart';
import 'package:sira_pro/presentation/widgets/common/loading_button.dart';

void main() {
  group('Authentication UI Components Tests', () {
    // Note: Full authentication screen tests require Firebase setup
    // These tests focus on reusable UI components

    testWidgets('CustomTextField widget works correctly', (
      WidgetTester tester,
    ) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: CustomTextField(
              controller: controller,
              label: 'Test Label',
              hintText: 'Test Hint',
              prefixIcon: Icons.email,
            ),
          ),
        ),
      );

      // Verify label and hint text
      expect(find.text('Test Label'), findsOneWidget);
      expect(find.text('Test Hint'), findsOneWidget);
      expect(find.byIcon(Icons.email), findsOneWidget);

      // Test text input
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      expect(controller.text, '<EMAIL>');
    });

    testWidgets('LoadingButton widget works correctly', (
      WidgetTester tester,
    ) async {
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: LoadingButton(
              onPressed: () => buttonPressed = true,
              isLoading: false,
              text: 'Test Button',
            ),
          ),
        ),
      );

      // Verify button text
      expect(find.text('Test Button'), findsOneWidget);

      // Test button press
      await tester.tap(find.byType(ElevatedButton));
      expect(buttonPressed, true);
    });

    testWidgets('LoadingButton shows loading state', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: LoadingButton(
              onPressed: () {},
              isLoading: true,
              text: 'Test Button',
            ),
          ),
        ),
      );

      // Verify loading state
      expect(find.text('Loading...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    // Note: Form validation tests require full screen setup with Firebase
    // These are skipped for now and would be added when Firebase is configured
  });
}
