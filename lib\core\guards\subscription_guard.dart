import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../providers/subscription_provider.dart';
import '../providers/base_auth_provider.dart';

/// Guard widget that checks subscription access before showing content
class SubscriptionGuard extends StatefulWidget {
  final Widget child;
  final String? redirectRoute;
  final bool requiresActiveSubscription;

  const SubscriptionGuard({
    super.key,
    required this.child,
    this.redirectRoute,
    this.requiresActiveSubscription = true,
  });

  @override
  State<SubscriptionGuard> createState() => _SubscriptionGuardState();
}

class _SubscriptionGuardState extends State<SubscriptionGuard> {
  bool _isChecking = true;
  bool _hasAccess = false;

  @override
  void initState() {
    super.initState();
    _checkAccess();
  }

  Future<void> _checkAccess() async {
    final authProvider = context.read<BaseAuthProvider>();
    final subscriptionProvider = context.read<SubscriptionProvider>();

    if (authProvider.user == null) {
      // User not authenticated, redirect to sign in
      if (mounted) {
        context.go('/signin');
      }
      return;
    }

    // Initialize subscription provider if needed
    if (subscriptionProvider.currentSubscription == null) {
      await subscriptionProvider.initialize(authProvider.user!.uid);
    }

    // Check subscription access
    final hasAccess = widget.requiresActiveSubscription
        ? subscriptionProvider.hasAccess
        : true;

    setState(() {
      _hasAccess = hasAccess;
      _isChecking = false;
    });

    // Redirect if no access
    if (!hasAccess && mounted) {
      final redirectTo = widget.redirectRoute ?? '/subscription';
      context.go(redirectTo);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isChecking) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (!_hasAccess) {
      return const Scaffold(
        body: Center(
          child: Text('Redirecting...'),
        ),
      );
    }

    return widget.child;
  }
}

/// Mixin for widgets that need subscription access control
mixin SubscriptionAccessMixin<T extends StatefulWidget> on State<T> {
  bool get requiresSubscription => true;
  
  bool _hasAccess = false;
  bool _isCheckingAccess = true;

  @override
  void initState() {
    super.initState();
    _checkSubscriptionAccess();
  }

  Future<void> _checkSubscriptionAccess() async {
    if (!requiresSubscription) {
      setState(() {
        _hasAccess = true;
        _isCheckingAccess = false;
      });
      return;
    }

    final authProvider = context.read<BaseAuthProvider>();
    final subscriptionProvider = context.read<SubscriptionProvider>();

    if (authProvider.user == null) {
      if (mounted) {
        context.go('/signin');
      }
      return;
    }

    // Initialize subscription if needed
    if (subscriptionProvider.currentSubscription == null) {
      await subscriptionProvider.initialize(authProvider.user!.uid);
    }

    final hasAccess = subscriptionProvider.hasAccess;

    setState(() {
      _hasAccess = hasAccess;
      _isCheckingAccess = false;
    });

    if (!hasAccess && mounted) {
      context.go('/subscription');
    }
  }

  Widget buildWithSubscriptionCheck(Widget child) {
    if (_isCheckingAccess) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (!_hasAccess) {
      return const Scaffold(
        body: Center(
          child: Text('Redirecting to subscription...'),
        ),
      );
    }

    return child;
  }

  bool get hasSubscriptionAccess => _hasAccess;
  bool get isCheckingSubscriptionAccess => _isCheckingAccess;
}

/// Utility class for subscription access checks
class SubscriptionAccessUtils {
  /// Check if user has access to a specific feature
  static bool hasFeatureAccess(
    BuildContext context,
    String feature,
  ) {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    return subscriptionProvider.isFeatureAvailable(feature);
  }

  /// Get feature limit for current subscription
  static dynamic getFeatureLimit(
    BuildContext context,
    String feature,
  ) {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    return subscriptionProvider.getFeatureLimit(feature);
  }

  /// Show subscription required dialog
  static void showSubscriptionRequiredDialog(
    BuildContext context, {
    String? title,
    String? message,
    VoidCallback? onUpgrade,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? 'Subscription Required'),
        content: Text(
          message ?? 
          'This feature requires an active subscription. Please upgrade your plan to continue.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: onUpgrade ?? () {
              Navigator.of(context).pop();
              context.go('/subscription');
            },
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }

  /// Show feature limit reached dialog
  static void showFeatureLimitDialog(
    BuildContext context,
    String feature, {
    String? message,
    VoidCallback? onUpgrade,
  }) {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    final limit = subscriptionProvider.getFeatureLimit(feature);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Limit Reached'),
        content: Text(
          message ?? 
          'You have reached the limit for this feature ($limit). Upgrade your subscription to get more access.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: onUpgrade ?? () {
              Navigator.of(context).pop();
              context.go('/subscription');
            },
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }

  /// Check and enforce feature usage limit
  static bool checkFeatureUsage(
    BuildContext context,
    String feature,
    int currentUsage, {
    VoidCallback? onLimitReached,
  }) {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    
    if (!subscriptionProvider.hasAccess) {
      showSubscriptionRequiredDialog(context);
      return false;
    }

    final limit = subscriptionProvider.getFeatureLimit(feature);
    
    // -1 means unlimited
    if (limit == -1) return true;
    
    if (currentUsage >= limit) {
      if (onLimitReached != null) {
        onLimitReached();
      } else {
        showFeatureLimitDialog(context, feature);
      }
      return false;
    }

    return true;
  }

  /// Get subscription status widget
  static Widget buildSubscriptionStatus(BuildContext context) {
    return Consumer<SubscriptionProvider>(
      builder: (context, subscriptionProvider, child) {
        if (!subscriptionProvider.hasAccess) {
          return Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange),
            ),
            child: Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'No active subscription. Some features may be limited.',
                    style: TextStyle(color: Colors.orange[800]),
                  ),
                ),
                TextButton(
                  onPressed: () => context.go('/subscription'),
                  child: const Text('Upgrade'),
                ),
              ],
            ),
          );
        }

        if (subscriptionProvider.isInTrial) {
          return Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue),
            ),
            child: Row(
              children: [
                const Icon(Icons.access_time, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    subscriptionProvider.statusText,
                    style: TextStyle(color: Colors.blue[800]),
                  ),
                ),
                TextButton(
                  onPressed: () => context.go('/subscription'),
                  child: const Text('Upgrade'),
                ),
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}
