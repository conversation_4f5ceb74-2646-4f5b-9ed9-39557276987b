import 'package:firebase_auth/firebase_auth.dart';

/// User model for SIRA application
class SiraUser {
  final String uid;
  final String email;
  final String? displayName;
  final bool emailVerified;
  final DateTime? createdAt;
  final DateTime? lastSignIn;

  const Sira<PERSON>ser({
    required this.uid,
    required this.email,
    this.displayName,
    required this.emailVerified,
    this.createdAt,
    this.lastSignIn,
  });

  /// Create SiraUser from Firebase User
  factory SiraUser.fromFirebaseUser(User firebaseUser) {
    return SiraUser(
      uid: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      displayName: firebaseUser.displayName,
      emailVerified: firebaseUser.emailVerified,
      createdAt: firebaseUser.metadata.creationTime,
      lastSignIn: firebaseUser.metadata.lastSignInTime,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'emailVerified': emailVerified,
      'createdAt': createdAt?.toIso8601String(),
      'lastSignIn': lastSignIn?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory SiraUser.fromJson(Map<String, dynamic> json) {
    return SiraUser(
      uid: json['uid'] ?? '',
      email: json['email'] ?? '',
      displayName: json['displayName'],
      emailVerified: json['emailVerified'] ?? false,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      lastSignIn: json['lastSignIn'] != null 
          ? DateTime.parse(json['lastSignIn']) 
          : null,
    );
  }

  /// Copy with new values
  SiraUser copyWith({
    String? uid,
    String? email,
    String? displayName,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastSignIn,
  }) {
    return SiraUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastSignIn: lastSignIn ?? this.lastSignIn,
    );
  }

  @override
  String toString() {
    return 'SiraUser(uid: $uid, email: $email, displayName: $displayName, emailVerified: $emailVerified)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SiraUser &&
        other.uid == uid &&
        other.email == email &&
        other.displayName == displayName &&
        other.emailVerified == emailVerified;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
        email.hashCode ^
        displayName.hashCode ^
        emailVerified.hashCode;
  }
}

/// Authentication result model
class AuthResult {
  final SiraUser? user;
  final String? error;
  final bool success;

  const AuthResult({
    this.user,
    this.error,
    required this.success,
  });

  /// Success result
  factory AuthResult.success(SiraUser user) {
    return AuthResult(
      user: user,
      success: true,
    );
  }

  /// Error result
  factory AuthResult.error(String error) {
    return AuthResult(
      error: error,
      success: false,
    );
  }
}
