import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'dart:io' show Platform;

/// Service for managing local notifications
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  /// Initialize the notification service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialize timezone data
      tz.initializeTimeZones();

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      // Initialize the plugin
      final bool? result = await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _isInitialized = result ?? false;

      if (_isInitialized) {
        await _requestPermissions();
      }

      return _isInitialized;
    } catch (e) {
      debugPrint('Error initializing notifications: $e');
      return false;
    }
  }

  /// Request notification permissions
  Future<bool> _requestPermissions() async {
    try {
      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin
                >();

        final bool? granted = await androidImplementation
            ?.requestNotificationsPermission();
        return granted ?? false;
      } else if (Platform.isIOS) {
        final bool? result = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin
            >()
            ?.requestPermissions(alert: true, badge: true, sound: true);
        return result ?? false;
      }
      return true;
    } catch (e) {
      debugPrint('Error requesting notification permissions: $e');
      return false;
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    debugPrint('Notification tapped: ${notificationResponse.payload}');
    // Handle notification tap based on payload
    // You can navigate to specific screens or perform actions here
  }

  /// Show immediate notification
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            'sira_general',
            'SIRA General',
            channelDescription: 'General notifications for SIRA app',
            importance: Importance.high,
            priority: Priority.high,
            showWhen: true,
          );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

  /// Schedule notification for future delivery
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            'sira_scheduled',
            'SIRA Scheduled',
            channelDescription: 'Scheduled notifications for SIRA app',
            importance: Importance.high,
            priority: Priority.high,
            showWhen: true,
          );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        _convertToTZDateTime(scheduledDate),
        platformChannelSpecifics,
        payload: payload,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
    }
  }

  /// Cancel specific notification
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
    } catch (e) {
      debugPrint('Error canceling notification: $e');
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
    } catch (e) {
      debugPrint('Error canceling all notifications: $e');
    }
  }

  /// Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      return await _flutterLocalNotificationsPlugin
          .pendingNotificationRequests();
    } catch (e) {
      debugPrint('Error getting pending notifications: $e');
      return [];
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin
                >();
        return await androidImplementation?.areNotificationsEnabled() ?? false;
      }
      return true; // iOS handles this at system level
    } catch (e) {
      debugPrint('Error checking notification status: $e');
      return false;
    }
  }

  /// Show subscription expiration notification
  Future<void> showSubscriptionExpirationNotification({
    required String subscriptionType,
    required DateTime expirationDate,
  }) async {
    final daysUntilExpiration = expirationDate
        .difference(DateTime.now())
        .inDays;

    String title;
    String body;

    if (daysUntilExpiration <= 0) {
      title = 'Subscription Expired';
      body =
          'Your $subscriptionType subscription has expired. Renew now to continue using SIRA.';
    } else if (daysUntilExpiration == 1) {
      title = 'Subscription Expires Tomorrow';
      body =
          'Your $subscriptionType subscription expires tomorrow. Renew now to avoid interruption.';
    } else if (daysUntilExpiration <= 3) {
      title = 'Subscription Expiring Soon';
      body =
          'Your $subscriptionType subscription expires in $daysUntilExpiration days. Renew now to continue using SIRA.';
    } else {
      title = 'Subscription Reminder';
      body =
          'Your $subscriptionType subscription expires in $daysUntilExpiration days.';
    }

    await showNotification(
      id: 1001, // Use consistent ID for subscription notifications
      title: title,
      body: body,
      payload: 'subscription_expiration',
    );
  }

  /// Schedule subscription expiration reminders
  Future<void> scheduleSubscriptionReminders({
    required String subscriptionType,
    required DateTime expirationDate,
  }) async {
    // Cancel existing subscription notifications
    await cancelNotification(1001);
    await cancelNotification(1002);
    await cancelNotification(1003);

    final now = DateTime.now();

    // Schedule 3-day reminder
    final threeDaysBefore = expirationDate.subtract(const Duration(days: 3));
    if (threeDaysBefore.isAfter(now)) {
      await scheduleNotification(
        id: 1001,
        title: 'Subscription Expiring Soon',
        body:
            'Your $subscriptionType subscription expires in 3 days. Renew now to continue using SIRA.',
        scheduledDate: threeDaysBefore,
        payload: 'subscription_reminder_3d',
      );
    }

    // Schedule 1-day reminder
    final oneDayBefore = expirationDate.subtract(const Duration(days: 1));
    if (oneDayBefore.isAfter(now)) {
      await scheduleNotification(
        id: 1002,
        title: 'Subscription Expires Tomorrow',
        body:
            'Your $subscriptionType subscription expires tomorrow. Renew now to avoid interruption.',
        scheduledDate: oneDayBefore,
        payload: 'subscription_reminder_1d',
      );
    }

    // Schedule expiration notification
    if (expirationDate.isAfter(now)) {
      await scheduleNotification(
        id: 1003,
        title: 'Subscription Expired',
        body:
            'Your $subscriptionType subscription has expired. Renew now to continue using SIRA.',
        scheduledDate: expirationDate,
        payload: 'subscription_expired',
      );
    }
  }

  /// Convert DateTime to TZDateTime for scheduling
  tz.TZDateTime _convertToTZDateTime(DateTime dateTime) {
    final location = tz.local;
    return tz.TZDateTime.from(dateTime, location);
  }
}
