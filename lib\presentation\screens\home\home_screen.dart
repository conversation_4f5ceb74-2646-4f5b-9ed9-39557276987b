import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../core/providers/base_auth_provider.dart';
import '../../../core/guards/subscription_guard.dart';

/// Home screen with subscription-gated features
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SubscriptionAccessMixin {
  // External URL for the main feature (replace with actual URL)
  static const String _externalUrl =
      'https://app.sira.ai/dashboard'; // Example SIRA service URL

  @override
  Widget build(BuildContext context) {
    return buildWithSubscriptionCheck(
      Scaffold(
        appBar: AppBar(
          title: const Text('SIRA'),
          actions: [
            IconButton(
              icon: const Icon(Icons.person),
              onPressed: () {
                // TODO: Navigate to profile screen
                _showProfileMenu(context);
              },
            ),
          ],
        ),
        body: Consumer2<BaseAuthProvider, SubscriptionProvider>(
          builder: (context, authProvider, subscriptionProvider, child) {
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppConstants.paddingL),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Welcome message
                        _buildWelcomeSection(authProvider),

                        const SizedBox(height: AppConstants.spacingL),

                        // Subscription status
                        SubscriptionAccessUtils.buildSubscriptionStatus(
                          context,
                        ),

                        const SizedBox(height: AppConstants.spacingL),

                        // Main feature access
                        _buildMainFeatureSection(subscriptionProvider),

                        const SizedBox(height: AppConstants.spacingXL),

                        // Usage Summary
                        _buildUsageSummary(),

                        const SizedBox(height: AppConstants.spacingXL),

                        // Subscription info
                        _buildSubscriptionInfo(subscriptionProvider),
                      ],
                    ),
                  ),
                ),
                // Footer
                _buildFooter(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BaseAuthProvider authProvider) {
    final displayName =
        authProvider.user?.displayName ??
        authProvider.user?.email.split('@').first ??
        'User';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome back, $displayName!',
          style: Theme.of(
            context,
          ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.spacingS),
        Text(
          'Your smart invoice and receipt assistant is ready to help you process documents, extract data, and manage your business finances efficiently.',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildMainFeatureSection(SubscriptionProvider subscriptionProvider) {
    final hasAccess = subscriptionProvider.hasAccess;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  size: 32,
                  color: hasAccess ? AppConstants.primaryPink : Colors.grey,
                ),
                const SizedBox(width: AppConstants.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Invoice & Receipt Assistant',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.spacingS),
                      Text(
                        'Access your smart document processing service',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacingL),

            if (hasAccess) ...[
              // Main access button - directly opens WebView
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _openInWebView,
                  icon: const Icon(Icons.web),
                  label: const Text('Access SIRA Service'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryPink,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppConstants.paddingM,
                    ),
                  ),
                ),
              ),
            ] else ...[
              // Upgrade required button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _showUpgradeDialog,
                  icon: const Icon(Icons.lock),
                  label: const Text('Upgrade Required'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppConstants.paddingM,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUsageSummary() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.bar_chart,
                  color: AppConstants.primaryPink,
                  size: 24,
                ),
                const SizedBox(width: AppConstants.spacingS),
                Text(
                  'Usage Summary',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.spacingS),
            Text(
              'A quick overview of your activity.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: AppConstants.spacingL),

            // Usage stats
            _buildUsageStatCard(
              count: '14',
              label: 'Invoices Generated',
              backgroundColor: AppConstants.primaryPink.withValues(alpha: 0.1),
            ),
            const SizedBox(height: AppConstants.spacingM),
            _buildUsageStatCard(
              count: '8',
              label: 'Receipts Processed',
              backgroundColor: AppConstants.primaryPink.withValues(alpha: 0.05),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageStatCard({
    required String count,
    required String label,
    required Color backgroundColor,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingL),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.radiusM),
      ),
      child: Column(
        children: [
          Text(
            count,
            style: Theme.of(context).textTheme.displayMedium?.copyWith(
              color: AppConstants.primaryPink,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.spacingS),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.primaryPink,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionInfo(SubscriptionProvider subscriptionProvider) {
    if (!subscriptionProvider.hasAccess) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subscription Details',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.spacingM),

            _buildInfoRow('Status', subscriptionProvider.statusText),

            if (subscriptionProvider.currentSubscriptionType != null)
              _buildInfoRow(
                'Plan',
                subscriptionProvider.currentSubscriptionType!.displayName,
              ),

            if (subscriptionProvider.nextBillingDate != null)
              _buildInfoRow(
                'Next Billing',
                _formatDate(subscriptionProvider.nextBillingDate!),
              ),

            const SizedBox(height: AppConstants.spacingM),

            TextButton(
              onPressed: () => context.go('/subscription'),
              child: const Text('Manage Subscription'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacingS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  void _openInWebView() {
    context.push(
      '/webview?url=${Uri.encodeComponent(_externalUrl)}&title=${Uri.encodeComponent('SIRA Service')}',
    );
  }

  void _showUpgradeDialog() {
    SubscriptionAccessUtils.showSubscriptionRequiredDialog(
      context,
      title: 'Subscription Required',
      message:
          'This feature requires an active subscription. Please upgrade to continue.',
    );
  }

  Widget _buildFooter() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.withValues(alpha: 0.3), width: 1),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingL,
            vertical: AppConstants.paddingM,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildFooterItem(
                icon: Icons.home,
                label: 'Home',
                isActive: true,
                onTap: () {
                  // Already on home screen
                },
              ),
              _buildFooterItem(
                icon: Icons.person,
                label: 'Profile',
                isActive: false,
                onTap: () {
                  context.go('/profile');
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooterItem({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isActive ? AppConstants.primaryPink : Colors.grey,
            size: 24,
          ),
          const SizedBox(height: AppConstants.spacingXS),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isActive ? AppConstants.primaryPink : Colors.grey,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  void _showProfileMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('Profile'),
            onTap: () {
              Navigator.pop(context);
              context.go('/profile');
            },
          ),
          ListTile(
            leading: const Icon(Icons.subscriptions),
            title: const Text('Subscription'),
            onTap: () {
              Navigator.pop(context);
              context.go('/subscription');
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Sign Out'),
            onTap: () async {
              Navigator.pop(context);
              final authProvider = context.read<BaseAuthProvider>();
              await authProvider.signOut();
              if (mounted) {
                context.go('/welcome');
              }
            },
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
