import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:email_validator/email_validator.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/base_auth_provider.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_button.dart';

/// Forgot Password screen for password reset
class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _handlePasswordReset() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = context.read<BaseAuthProvider>();

    final success = await authProvider.sendPasswordResetEmail(
      email: _emailController.text,
    );

    if (success && mounted) {
      setState(() {
        _emailSent = true;
      });
    }
  }

  void _navigateToSignIn() {
    context.go('/signin');
  }

  void _resendEmail() {
    setState(() {
      _emailSent = false;
    });
    _handlePasswordReset();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reset Password'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/signin'),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.spacingL),
          child: _emailSent ? _buildEmailSentView() : _buildResetForm(),
        ),
      ),
    );
  }

  Widget _buildResetForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: AppConstants.spacingXXL),

          // Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppConstants.lightPink,
              borderRadius: BorderRadius.circular(AppConstants.radiusXL),
            ),
            child: const Icon(
              Icons.lock_reset,
              size: 40,
              color: AppConstants.primaryPink,
            ),
          ),

          const SizedBox(height: AppConstants.spacingXL),

          // Title
          Text(
            'Forgot Password?',
            style: Theme.of(context).textTheme.displayLarge?.copyWith(
              color: AppConstants.primaryPink,
              fontWeight: FontWeight.bold,
              fontSize: 32,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.spacingM),

          // Description
          Text(
            'No worries! Enter your email address and we\'ll send you a link to reset your password.',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppConstants.mediumGray),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.spacingXXL),

          // Email field
          CustomTextField(
            controller: _emailController,
            label: 'Email Address',
            hintText: 'Enter your email',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email_outlined,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!EmailValidator.validate(value)) {
                return 'Please enter a valid email address';
              }
              return null;
            },
          ),

          const SizedBox(height: AppConstants.spacingXL),

          // Reset button
          Consumer<BaseAuthProvider>(
            builder: (context, authProvider, child) {
              return LoadingButton(
                onPressed: _handlePasswordReset,
                isLoading: authProvider.isLoading,
                text: 'Send Reset Link',
              );
            },
          ),

          const SizedBox(height: AppConstants.spacingL),

          // Error message
          Consumer<BaseAuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.error != null) {
                return Container(
                  padding: const EdgeInsets.all(AppConstants.spacingM),
                  decoration: BoxDecoration(
                    color: AppConstants.errorRed.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.radiusM),
                    border: Border.all(
                      color: AppConstants.errorRed.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: AppConstants.errorRed,
                        size: 20,
                      ),
                      const SizedBox(width: AppConstants.spacingS),
                      Expanded(
                        child: Text(
                          authProvider.error!,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppConstants.errorRed),
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),

          const SizedBox(height: AppConstants.spacingXL),

          // Back to sign in
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Remember your password? ',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              TextButton(
                onPressed: _navigateToSignIn,
                child: const Text('Sign In'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmailSentView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: AppConstants.spacingXXL),

        // Success icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppConstants.successGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.radiusXL),
          ),
          child: const Icon(
            Icons.mark_email_read,
            size: 40,
            color: AppConstants.successGreen,
          ),
        ),

        const SizedBox(height: AppConstants.spacingXL),

        // Title
        Text(
          'Check Your Email',
          style: Theme.of(context).textTheme.displayLarge,
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: AppConstants.spacingM),

        // Description
        Text(
          'We\'ve sent a password reset link to ${_emailController.text}',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: AppConstants.mediumGray),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: AppConstants.spacingL),

        Text(
          'Please check your email and follow the instructions to reset your password.',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppConstants.mediumGray),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: AppConstants.spacingXXL),

        // Resend button
        LoadingButton(
          onPressed: _resendEmail,
          isLoading: false,
          text: 'Resend Email',
          isOutlined: true,
        ),

        const SizedBox(height: AppConstants.spacingL),

        // Back to sign in
        LoadingButton(
          onPressed: _navigateToSignIn,
          isLoading: false,
          text: 'Back to Sign In',
        ),
      ],
    );
  }
}
