import '../models/user_model.dart';
import '../services/auth_service.dart';
import 'base_auth_provider.dart';

/// Authentication state management provider
class AuthProvider extends BaseAuthProvider {
  final AuthService _authService = AuthService();

  SiraUser? _user;
  bool _isLoading = false;
  String? _error;

  /// Current user
  @override
  SiraUser? get user => _user;

  /// Loading state
  @override
  bool get isLoading => _isLoading;

  /// Error message
  @override
  String? get error => _error;

  /// Whether user is authenticated
  @override
  bool get isAuthenticated => _user != null;

  /// Initialize auth provider and listen to auth state changes
  @override
  void initialize() {
    _authService.authStateChanges.listen((user) {
      _user = user;
      notifyListeners();
    });
  }

  /// Sign in with email and password
  @override
  Future<bool> signIn({required String email, required String password}) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.success && result.user != null) {
        _user = result.user;
        _setLoading(false);
        return true;
      } else {
        _setError(result.error ?? 'Sign in failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred');
      _setLoading(false);
      return false;
    }
  }

  /// Sign in with Google
  @override
  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.signInWithGoogle();

      if (result.success && result.user != null) {
        _user = result.user;
        _setLoading(false);
        return true;
      } else {
        _setError(result.error ?? 'Google sign-in failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred during Google sign-in');
      _setLoading(false);
      return false;
    }
  }

  /// Sign up with email and password
  @override
  Future<bool> signUp({required String email, required String password}) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.success && result.user != null) {
        _user = result.user;
        _setLoading(false);
        return true;
      } else {
        _setError(result.error ?? 'Sign up failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred');
      _setLoading(false);
      return false;
    }
  }

  /// Send password reset email
  @override
  Future<bool> sendPasswordResetEmail({required String email}) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.sendPasswordResetEmail(email: email);

      if (result.success) {
        _setLoading(false);
        return true;
      } else {
        _setError(result.error ?? 'Failed to send password reset email');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred');
      _setLoading(false);
      return false;
    }
  }

  /// Send email verification
  @override
  Future<bool> sendEmailVerification() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.sendEmailVerification();

      if (result.success) {
        _setLoading(false);
        return true;
      } else {
        _setError(result.error ?? 'Failed to send verification email');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred');
      _setLoading(false);
      return false;
    }
  }

  /// Update password
  @override
  Future<bool> updatePassword({required String newPassword}) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.updatePassword(
        newPassword: newPassword,
      );

      if (result.success && result.user != null) {
        _user = result.user;
        _setLoading(false);
        return true;
      } else {
        _setError(result.error ?? 'Failed to update password');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('An unexpected error occurred');
      _setLoading(false);
      return false;
    }
  }

  /// Sign out
  @override
  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.signOut();
      _user = null;
      _setLoading(false);
    } catch (e) {
      _setError('Failed to sign out');
      _setLoading(false);
    }
  }

  /// Clear error message
  @override
  void clearError() {
    _clearError();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
