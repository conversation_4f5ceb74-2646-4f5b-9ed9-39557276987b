import 'package:flutter/foundation.dart';
import '../models/user_model.dart';

/// Base authentication provider interface
abstract class BaseAuthProvider extends ChangeNotifier {
  /// Current user
  SiraUser? get user;

  /// Loading state
  bool get isLoading;

  /// Error message
  String? get error;

  /// Whether user is authenticated
  bool get isAuthenticated;

  /// Initialize auth provider
  void initialize();

  /// Sign in with email and password
  Future<bool> signIn({required String email, required String password});

  /// Sign in with Google
  Future<bool> signInWithGoogle();

  /// Sign up with email and password
  Future<bool> signUp({
    required String email,
    required String password,
    String? displayName,
  });

  /// Send password reset email
  Future<bool> sendPasswordResetEmail({required String email});

  /// Send email verification
  Future<bool> sendEmailVerification();

  /// Update password
  Future<bool> updatePassword({required String newPassword});

  /// Sign out
  Future<void> signOut();

  /// Clear error message
  void clearError();
}
