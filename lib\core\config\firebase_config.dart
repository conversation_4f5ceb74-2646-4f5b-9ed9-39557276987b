import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

/// Firebase configuration for SIRA app
///
/// Note: This is a placeholder configuration.
/// In a real project, you would:
/// 1. Create a Firebase project at https://console.firebase.google.com/
/// 2. Add your Android/iOS apps to the project
/// 3. Download google-services.json (Android) and GoogleService-Info.plist (iOS)
/// 4. Run `flutterfire configure` to generate firebase_options.dart
/// 5. Replace this placeholder with the generated configuration
class FirebaseConfig {
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'your-android-api-key',
    appId: 'your-android-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    storageBucket: 'your-project-id.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'your-ios-api-key',
    appId: 'your-ios-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    storageBucket: 'your-project-id.appspot.com',
    iosBundleId: 'com.example.siraPro',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'your-web-api-key',
    appId: 'your-web-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    storageBucket: 'your-project-id.appspot.com',
    authDomain: 'your-project-id.firebaseapp.com',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'your-windows-api-key',
    appId: 'your-windows-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    storageBucket: 'your-project-id.appspot.com',
    authDomain: 'your-project-id.firebaseapp.com',
  );

  /// Get Firebase options for current platform
  static FirebaseOptions get currentPlatform {
    // This is a placeholder implementation
    // In a real app, use the generated firebase_options.dart
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.windows:
        return windows;
      default:
        return web;
    }
  }
}

/// Instructions for setting up Firebase:
///
/// 1. Go to https://console.firebase.google.com/
/// 2. Create a new project or select existing one
/// 3. Enable Authentication and set up Email/Password provider
/// 4. Add your Flutter app to the project
/// 5. Download configuration files:
///    - google-services.json for Android (place in android/app/)
///    - GoogleService-Info.plist for iOS (place in ios/Runner/)
/// 6. Install Firebase CLI: npm install -g firebase-tools
/// 7. Install FlutterFire CLI: dart pub global activate flutterfire_cli
/// 8. Run: flutterfire configure
/// 9. Replace this file with the generated firebase_options.dart
