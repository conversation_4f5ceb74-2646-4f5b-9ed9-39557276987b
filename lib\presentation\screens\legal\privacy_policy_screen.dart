import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';

/// Privacy Policy screen
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.spacingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              Text(
                'Privacy Policy',
                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                  color: AppConstants.primaryPink,
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                ),
              ),

              const SizedBox(height: AppConstants.spacingL),

              // Last Updated
              Text(
                'Last Updated: [18 July 2025]',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[600],
                ),
              ),

              const SizedBox(height: AppConstants.spacingXL),

              // Introduction
              Text(
                'This Privacy Policy describes how we collect, use, and handle your information when you use our Smart Invoice-Receipt Assistant application (the "Service"). Your privacy is important to us, and we are committed to protecting it.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  height: 1.6,
                ),
              ),

              const SizedBox(height: AppConstants.spacingL),

              _buildSection(
                context,
                '1. Information We Collect',
                'To provide our Service, we collect the following types of information:\n• Information You Provide Directly:\no Your Business Information: This includes your business name, address, phone number, email, and any logo image you choose to upload.\no Your Customer\'s Information: This includes names, addresses, contact details, and billing information for the customers you are invoicing.\no Invoice & Receipt Details: This includes line item descriptions, quantities, prices, currency, invoice numbers, dates, and payment terms.\n• Information Processed for AI Features:\no When you use our AI-powered features, specific, limited pieces of information are sent to our third-party AI service provider (e.g., Google) to generate a response. This includes:\n\tPartial business or customer information for the "Auto-fill" feature.\n\tPartial line item descriptions for the "Suggest Line Items" feature.\no We only send the minimum information necessary to fulfill the feature\'s request. This data is used by the provider to generate the result and is subject to their privacy policies. We do not use the output from these models to train our own.\n• Information We Do Not Store Long-Term:\no This Service is designed to be stateless. The information you enter is held temporarily to generate your PDF document. We do not save your invoices, receipts, or customer lists on our servers after you close your session. The PDF is generated in your browser and it is your responsibility to save it.',
              ),

              _buildSection(
                context,
                '2. How We Use Your Information',
                'We use the information we collect for the following purposes:\n• To Provide and Operate the Service: The primary use of your data is to generate the invoice or receipt PDF as you have requested.\n• To Power AI Features: To provide you with intelligent suggestions and auto-filling capabilities.\n• To Improve the Service: We may analyze aggregated, anonymized usage data to understand how our Service is used and how we can improve it.\n• For Customer Support: To respond to your questions or troubleshoot issues.',
              ),

              _buildSection(
                context,
                '3. Data Sharing and Disclosure',
                'We do not sell or rent your personal information. We may share your information only in the following limited circumstances:\n• With AI Service Providers: As mentioned above, we share limited, contextual data with our third-party AI service providers to enable the smart features of the app.\n• For Legal Reasons: We may disclose your information if required to do so by law or in the good faith belief that such action is necessary to comply with a legal obligation, protect our rights or property, or prevent fraud or illegal activity.',
              ),

              _buildSection(
                context,
                '4. Data Security',
                'We implement reasonable security measures to protect the information you provide. All communication between your browser and our servers is encrypted using standard technologies (e.g., SSL/TLS). However, no method of transmission over the Internet or electronic storage is 100% secure.',
              ),

              _buildSection(
                context,
                '5. Data Retention',
                'As stated, we do not have a user database and do not store your invoice data or customer lists on our servers after your session ends. The logo you upload is temporarily processed and is not retained.',
              ),

              _buildSection(
                context,
                '6. Children\'s Privacy',
                'Our Service is not directed to individuals under the age of 13, and we do not knowingly collect personal information from children.',
              ),

              _buildSection(
                context,
                '7. Changes to This Privacy Policy',
                'We may update this Privacy Policy from time to time. We will notify you of any significant changes by posting the new policy on this page. We encourage you to review this Privacy Policy periodically.',
              ),

              _buildSection(
                context,
                '8. Contact Us',
                'If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>',
              ),

              const SizedBox(height: AppConstants.spacingXXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryBlue,
          ),
        ),
        const SizedBox(height: AppConstants.spacingS),
        Text(
          content,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            height: 1.6,
          ),
        ),
        const SizedBox(height: AppConstants.spacingL),
      ],
    );
  }
}
