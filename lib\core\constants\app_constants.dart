import 'package:flutter/material.dart';

/// App-wide constants for SIRA (Smart Invoice-Receipt Assistant)
class AppConstants {
  // App Information
  static const String appName = 'SIRA';
  static const String appFullName = 'Smart Invoice-Receipt Assistant';
  static const String appVersion = '1.0.0';

  // Brand Colors - Pink/Magenta Theme
  static const Color primaryPink = Color(0xFFE91E63);
  static const Color primaryPinkDark = Color(0xFFC2185B);
  static const Color secondaryPink = Color(0xFFEC407A);
  static const Color lightPink = Color(0xFFFCE4EC);

  // Legacy blue colors (for backward compatibility)
  static const Color primaryBlue = primaryPink;
  static const Color primaryBlueDark = primaryPinkDark;
  static const Color secondaryBlue = secondaryPink;
  static const Color lightBlue = lightPink;

  // Neutral Colors - High Contrast for Readability
  static const Color darkGray = Color(0xFF212121);
  static const Color mediumGray = Color(0xFF757575);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color white = Color(0xFFFFFFFF);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);

  // Status Colors
  static const Color successGreen = Color(0xFF10B981);
  static const Color warningYellow = Color(0xFFF59E0B);
  static const Color errorRed = Color(0xFFEF4444);

  // Spacing
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 12.0;
  static const double paddingL = 16.0;
  static const double paddingXL = 24.0;
  static const double paddingXXL = 32.0;

  // Border Radius
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;

  // Border Radius (alternative naming for consistency)
  static const double borderRadiusS = radiusS;
  static const double borderRadiusM = radiusM;
  static const double borderRadiusL = radiusL;
  static const double borderRadiusXL = radiusXL;

  // Animation Durations
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  static const Duration splashDuration = Duration(milliseconds: 2500);

  // Text Sizes
  static const double textSizeXS = 12.0;
  static const double textSizeS = 14.0;
  static const double textSizeM = 16.0;
  static const double textSizeL = 18.0;
  static const double textSizeXL = 24.0;
  static const double textSizeXXL = 32.0;

  // Button Heights
  static const double buttonHeightS = 40.0;
  static const double buttonHeightM = 48.0;
  static const double buttonHeightL = 56.0;

  // App Strings
  static const String welcomeTitle = 'Welcome to SIRA';
  static const String welcomeSubtitle = 'Your Smart Invoice-Receipt Assistant';
  static const String welcomeDescription =
      'Generate invoices and receipts the smart way—let our AI assistant handle the hard work so you can stay efficient.';
  static const String getStartedButton = 'Get Started';
  static const String signInButton = 'Sign In';
  static const String signUpButton = 'Sign Up';
  static const String continueButton = 'Continue';

  // Asset Paths
  static const String logoPath = 'assets/images/sira_logo.png';
  static const String logoWhitePath = 'assets/images/sira_logo_white.png';
}
