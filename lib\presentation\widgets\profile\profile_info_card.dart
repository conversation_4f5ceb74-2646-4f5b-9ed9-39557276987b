import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

/// Widget for displaying profile information in a card format
class ProfileInfoCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final String? subtitle;
  final bool isEditable;
  final Color? valueColor;
  final VoidCallback? onEdit;

  const ProfileInfoCard({
    super.key,
    required this.icon,
    required this.title,
    required this.value,
    this.subtitle,
    this.isEditable = false,
    this.valueColor,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingM),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusM),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingS),
            decoration: BoxDecoration(
              color: AppConstants.primaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusS),
            ),
            child: Icon(
              icon,
              color: AppConstants.primaryBlue,
              size: 20,
            ),
          ),
          
          const SizedBox(width: AppConstants.spacingM),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                const SizedBox(height: AppConstants.spacingXS),
                
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: valueColor,
                  ),
                ),
                
                if (subtitle != null) ...[
                  const SizedBox(height: AppConstants.spacingXS),
                  Text(
                    subtitle!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Edit button
          if (isEditable) ...[
            const SizedBox(width: AppConstants.spacingS),
            IconButton(
              onPressed: onEdit,
              icon: const Icon(Icons.edit),
              iconSize: 20,
              color: AppConstants.primaryBlue,
              tooltip: 'Edit $title',
            ),
          ],
        ],
      ),
    );
  }
}
