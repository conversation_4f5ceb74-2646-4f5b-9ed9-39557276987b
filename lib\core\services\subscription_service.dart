import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/subscription_model.dart';
import '../config/subscription_config.dart';

/// Service for managing subscriptions and payments
class SubscriptionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Uuid _uuid = const Uuid();

  // Collection names
  static const String _subscriptionsCollection = 'subscriptions';
  static const String _paymentsCollection = 'payments';

  /// Get user's current subscription
  Future<Subscription?> getCurrentSubscription(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_subscriptionsCollection)
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: SubscriptionStatus.active.name)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) return null;

      final doc = querySnapshot.docs.first;
      return Subscription.fromJson({'id': doc.id, ...doc.data()});
    } catch (e) {
      debugPrint('Error getting current subscription: $e');
      return null;
    }
  }

  /// Get all user subscriptions
  Future<List<Subscription>> getUserSubscriptions(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_subscriptionsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        return Subscription.fromJson({'id': doc.id, ...doc.data()});
      }).toList();
    } catch (e) {
      debugPrint('Error getting user subscriptions: $e');
      return [];
    }
  }

  /// Check if user has access to features
  Future<bool> hasAccess(String userId) async {
    final subscription = await getCurrentSubscription(userId);
    return subscription?.isActive ?? false;
  }

  /// Check if user can start trial
  Future<bool> canStartTrial(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_subscriptionsCollection)
          .where('userId', isEqualTo: userId)
          .where('type', isEqualTo: SubscriptionType.trial.name)
          .limit(1)
          .get();

      return querySnapshot.docs.isEmpty;
    } catch (e) {
      debugPrint('Error checking trial eligibility: $e');
      return false;
    }
  }

  /// Start free trial
  Future<Subscription?> startFreeTrial(String userId) async {
    try {
      // Check if user can start trial
      final canStart = await canStartTrial(userId);
      if (!canStart) {
        throw Exception('User has already used their free trial');
      }

      final now = DateTime.now();
      final trialEndDate = SubscriptionConfig.getTrialEndDate();

      final subscription = Subscription(
        id: _uuid.v4(),
        userId: userId,
        type: SubscriptionType.trial,
        status: SubscriptionStatus.active,
        startDate: now,
        trialEndDate: trialEndDate,
        amount: SubscriptionConfig.trialPrice,
        currency: SubscriptionConfig.defaultCurrency,
        createdAt: now,
        updatedAt: now,
      );

      // Save to Firestore
      await _firestore
          .collection(_subscriptionsCollection)
          .doc(subscription.id)
          .set(subscription.toJson());

      return subscription;
    } catch (e) {
      debugPrint('Error starting free trial: $e');
      return null;
    }
  }

  /// Create paid subscription
  Future<Subscription?> createPaidSubscription({
    required String userId,
    required SubscriptionType type,
    required String orderId,
  }) async {
    try {
      if (type == SubscriptionType.trial) {
        throw Exception('Use startFreeTrial for trial subscriptions');
      }

      final now = DateTime.now();
      final endDate = SubscriptionConfig.calculateEndDate(type, now);
      final plan = SubscriptionConfig.getPlanByType(type);

      if (plan == null) {
        throw Exception('Invalid subscription type');
      }

      final subscription = Subscription(
        id: _uuid.v4(),
        userId: userId,
        type: type,
        status: SubscriptionStatus.active,
        startDate: now,
        endDate: endDate,
        amount: plan.price,
        currency: SubscriptionConfig.defaultCurrency,
        orderId: orderId,
        createdAt: now,
        updatedAt: now,
      );

      // Deactivate any existing active subscriptions
      await _deactivateExistingSubscriptions(userId);

      // Save new subscription to Firestore
      await _firestore
          .collection(_subscriptionsCollection)
          .doc(subscription.id)
          .set(subscription.toJson());

      return subscription;
    } catch (e) {
      debugPrint('Error creating paid subscription: $e');
      return null;
    }
  }

  /// Update subscription status
  Future<bool> updateSubscriptionStatus({
    required String subscriptionId,
    required SubscriptionStatus status,
  }) async {
    try {
      await _firestore
          .collection(_subscriptionsCollection)
          .doc(subscriptionId)
          .update({
            'status': status.name,
            'updatedAt': DateTime.now().toIso8601String(),
          });

      return true;
    } catch (e) {
      debugPrint('Error updating subscription status: $e');
      return false;
    }
  }

  /// Cancel subscription
  Future<bool> cancelSubscription(String subscriptionId) async {
    try {
      await _firestore
          .collection(_subscriptionsCollection)
          .doc(subscriptionId)
          .update({
            'status': SubscriptionStatus.cancelled.name,
            'updatedAt': DateTime.now().toIso8601String(),
          });

      return true;
    } catch (e) {
      debugPrint('Error cancelling subscription: $e');
      return false;
    }
  }

  /// Check and update expired subscriptions
  Future<void> checkExpiredSubscriptions() async {
    try {
      final now = DateTime.now();

      // Query active subscriptions
      final querySnapshot = await _firestore
          .collection(_subscriptionsCollection)
          .where('status', isEqualTo: SubscriptionStatus.active.name)
          .get();

      final batch = _firestore.batch();
      int updateCount = 0;

      for (final doc in querySnapshot.docs) {
        final subscription = Subscription.fromJson({
          'id': doc.id,
          ...doc.data(),
        });

        bool shouldExpire = false;

        // Check trial expiration
        if (subscription.type == SubscriptionType.trial &&
            subscription.trialEndDate != null &&
            now.isAfter(subscription.trialEndDate!)) {
          shouldExpire = true;
        }

        // Check paid subscription expiration
        if (subscription.type != SubscriptionType.trial &&
            subscription.endDate != null &&
            now.isAfter(subscription.endDate!)) {
          shouldExpire = true;
        }

        if (shouldExpire) {
          batch.update(doc.reference, {
            'status': SubscriptionStatus.expired.name,
            'updatedAt': now.toIso8601String(),
          });
          updateCount++;
        }
      }

      if (updateCount > 0) {
        await batch.commit();
        debugPrint('Updated $updateCount expired subscriptions');
      }
    } catch (e) {
      debugPrint('Error checking expired subscriptions: $e');
    }
  }

  /// Get subscription by ID
  Future<Subscription?> getSubscriptionById(String subscriptionId) async {
    try {
      final doc = await _firestore
          .collection(_subscriptionsCollection)
          .doc(subscriptionId)
          .get();

      if (!doc.exists) return null;

      return Subscription.fromJson({'id': doc.id, ...doc.data()!});
    } catch (e) {
      debugPrint('Error getting subscription by ID: $e');
      return null;
    }
  }

  /// Stream of user's current subscription
  Stream<Subscription?> subscriptionStream(String userId) {
    return _firestore
        .collection(_subscriptionsCollection)
        .where('userId', isEqualTo: userId)
        .where('status', isEqualTo: SubscriptionStatus.active.name)
        .orderBy('createdAt', descending: true)
        .limit(1)
        .snapshots()
        .map((snapshot) {
          if (snapshot.docs.isEmpty) return null;

          final doc = snapshot.docs.first;
          return Subscription.fromJson({'id': doc.id, ...doc.data()});
        });
  }

  /// Private method to deactivate existing subscriptions
  Future<void> _deactivateExistingSubscriptions(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_subscriptionsCollection)
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: SubscriptionStatus.active.name)
          .get();

      if (querySnapshot.docs.isEmpty) return;

      final batch = _firestore.batch();
      final now = DateTime.now();

      for (final doc in querySnapshot.docs) {
        batch.update(doc.reference, {
          'status': SubscriptionStatus.cancelled.name,
          'updatedAt': now.toIso8601String(),
        });
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error deactivating existing subscriptions: $e');
    }
  }

  /// Save payment record
  Future<void> savePaymentRecord({
    required String userId,
    required String subscriptionId,
    required String orderId,
    required double amount,
    required String currency,
    Map<String, dynamic>? paymentDetails,
  }) async {
    try {
      final paymentRecord = {
        'id': _uuid.v4(),
        'userId': userId,
        'subscriptionId': subscriptionId,
        'orderId': orderId,
        'amount': amount,
        'currency': currency,
        'status': 'completed',
        'paymentDetails': paymentDetails,
        'createdAt': DateTime.now().toIso8601String(),
      };

      await _firestore
          .collection(_paymentsCollection)
          .doc(paymentRecord['id'] as String)
          .set(paymentRecord);
    } catch (e) {
      debugPrint('Error saving payment record: $e');
    }
  }
}
