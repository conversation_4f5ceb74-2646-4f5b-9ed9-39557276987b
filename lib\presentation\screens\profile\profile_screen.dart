import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/base_auth_provider.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../core/guards/subscription_guard.dart';
import '../../widgets/profile/profile_info_card.dart';
import '../../widgets/profile/password_change_dialog.dart';

/// Profile screen for user account management
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SubscriptionAccessMixin {
  @override
  bool get requiresSubscription => false; // Profile should be accessible to all authenticated users

  @override
  Widget build(BuildContext context) {
    return buildWithSubscriptionCheck(
      Scaffold(
        appBar: AppBar(
          title: const Text('Profile'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go('/home'),
          ),
        ),
        body: Consumer2<BaseAuthProvider, SubscriptionProvider>(
          builder: (context, authProvider, subscriptionProvider, child) {
            final user = authProvider.user;

            if (user == null) {
              return const Center(
                child: Text('Please sign in to view your profile'),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header
                  _buildProfileHeader(user.email, user.displayName),

                  const SizedBox(height: AppConstants.spacingXL),

                  // User Information Section
                  _buildUserInfoSection(user),

                  const SizedBox(height: AppConstants.spacingL),

                  // Account Security Section
                  _buildSecuritySection(authProvider),

                  const SizedBox(height: AppConstants.spacingL),

                  // Subscription Information
                  _buildSubscriptionSection(subscriptionProvider),

                  const SizedBox(height: AppConstants.spacingXL),

                  // Account Actions
                  _buildAccountActions(authProvider),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileHeader(String email, String? displayName) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryPink,
            AppConstants.primaryPink.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusL),
      ),
      child: Column(
        children: [
          // Profile Avatar
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Text(
              _getInitials(displayName ?? email),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),

          const SizedBox(height: AppConstants.spacingM),

          // User Name
          Text(
            displayName ?? email.split('@').first,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: AppConstants.spacingS),

          // User Email
          Text(
            email,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoSection(user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Information',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: AppConstants.spacingL),

            ProfileInfoCard(
              icon: Icons.email,
              title: 'Email Address',
              value: user.email,
              isEditable: false,
              subtitle: 'Your email address cannot be changed',
            ),

            const SizedBox(height: AppConstants.spacingM),

            ProfileInfoCard(
              icon: Icons.person,
              title: 'Display Name',
              value: user.displayName ?? 'Not set',
              isEditable: false,
              subtitle: user.displayName == null ? 'No display name set' : null,
            ),

            const SizedBox(height: AppConstants.spacingM),

            ProfileInfoCard(
              icon: Icons.verified_user,
              title: 'Email Verification',
              value: user.emailVerified ? 'Verified' : 'Not Verified',
              isEditable: false,
              valueColor: user.emailVerified
                  ? AppConstants.successGreen
                  : AppConstants.errorRed,
              subtitle: user.emailVerified
                  ? 'Your email has been verified'
                  : 'Please verify your email address',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySection(BaseAuthProvider authProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Security',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: AppConstants.spacingL),

            ListTile(
              leading: const Icon(Icons.lock, color: AppConstants.primaryBlue),
              title: const Text('Change Password'),
              subtitle: const Text('Update your account password'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showPasswordChangeDialog(authProvider),
            ),

            const Divider(),

            ListTile(
              leading: const Icon(
                Icons.security,
                color: AppConstants.primaryBlue,
              ),
              title: const Text('Account Security'),
              subtitle: const Text('Manage your account security settings'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showSecurityInfo(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionSection(SubscriptionProvider subscriptionProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subscription',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: AppConstants.spacingL),

            ListTile(
              leading: Icon(
                subscriptionProvider.hasAccess
                    ? Icons.check_circle
                    : Icons.cancel,
                color: subscriptionProvider.hasAccess
                    ? AppConstants.successGreen
                    : AppConstants.errorRed,
              ),
              title: Text(subscriptionProvider.statusText),
              subtitle: Text(
                subscriptionProvider.hasAccess
                    ? 'You have access to all features'
                    : 'Upgrade to access premium features',
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.go('/subscription'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountActions(BaseAuthProvider authProvider) {
    return Column(
      children: [
        // Logout Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _showLogoutDialog(authProvider),
            icon: const Icon(Icons.logout),
            label: const Text('Sign Out'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorRed,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                vertical: AppConstants.paddingM,
              ),
            ),
          ),
        ),

        const SizedBox(height: AppConstants.spacingM),

        // App Version Info
        Text(
          'SIRA v1.0.0',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return 'U';

    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else {
      return name[0].toUpperCase();
    }
  }

  void _showPasswordChangeDialog(BaseAuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => PasswordChangeDialog(authProvider: authProvider),
    );
  }

  void _showSecurityInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Account Security'),
        content: const Text(
          'Your account is protected with industry-standard security measures. '
          'We recommend using a strong, unique password and keeping your account information up to date.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BaseAuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await authProvider.signOut();
              if (mounted) {
                context.go('/welcome');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorRed,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
