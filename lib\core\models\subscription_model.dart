/// Subscription model for SIRA application
class Subscription {
  final String id;
  final String userId;
  final SubscriptionType type;
  final SubscriptionStatus status;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime? trialEndDate;
  final double amount;
  final String currency;
  final String? orderId; // Generic order ID for tracking
  final DateTime createdAt;
  final DateTime updatedAt;

  const Subscription({
    required this.id,
    required this.userId,
    required this.type,
    required this.status,
    required this.startDate,
    this.endDate,
    this.trialEndDate,
    required this.amount,
    required this.currency,
    this.orderId,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Check if subscription is currently active
  bool get isActive {
    final now = DateTime.now();

    // Check if trial is active
    if (type == SubscriptionType.trial && trialEndDate != null) {
      return now.isBefore(trialEndDate!) && status == SubscriptionStatus.active;
    }

    // Check if paid subscription is active
    if (endDate != null) {
      return now.isBefore(endDate!) && status == SubscriptionStatus.active;
    }

    return status == SubscriptionStatus.active;
  }

  /// Check if subscription is in trial period
  bool get isInTrial {
    if (type != SubscriptionType.trial || trialEndDate == null) return false;
    return DateTime.now().isBefore(trialEndDate!) &&
        status == SubscriptionStatus.active;
  }

  /// Check if subscription has expired
  bool get isExpired {
    final now = DateTime.now();

    if (type == SubscriptionType.trial && trialEndDate != null) {
      return now.isAfter(trialEndDate!);
    }

    if (endDate != null) {
      return now.isAfter(endDate!);
    }

    return status == SubscriptionStatus.expired;
  }

  /// Get remaining days for subscription
  int get remainingDays {
    final now = DateTime.now();
    DateTime? expiryDate;

    if (type == SubscriptionType.trial && trialEndDate != null) {
      expiryDate = trialEndDate;
    } else if (endDate != null) {
      expiryDate = endDate;
    }

    if (expiryDate == null) return 0;

    final difference = expiryDate.difference(now);
    return difference.inDays > 0 ? difference.inDays : 0;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.name,
      'status': status.name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'trialEndDate': trialEndDate?.toIso8601String(),
      'amount': amount,
      'currency': currency,
      'orderId': orderId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      type: SubscriptionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SubscriptionType.trial,
      ),
      status: SubscriptionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SubscriptionStatus.inactive,
      ),
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      trialEndDate: json['trialEndDate'] != null
          ? DateTime.parse(json['trialEndDate'])
          : null,
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'USD',
      orderId: json['orderId'] ?? json['paypalOrderId'], // Support legacy data
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Copy with new values
  Subscription copyWith({
    String? id,
    String? userId,
    SubscriptionType? type,
    SubscriptionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? trialEndDate,
    double? amount,
    String? currency,
    String? orderId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Subscription(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      trialEndDate: trialEndDate ?? this.trialEndDate,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      orderId: orderId ?? this.orderId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Subscription(id: $id, userId: $userId, type: $type, status: $status, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subscription &&
        other.id == id &&
        other.userId == userId &&
        other.type == type &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^ userId.hashCode ^ type.hashCode ^ status.hashCode;
  }
}

/// Subscription types available in the app
enum SubscriptionType {
  trial('Free Trial', 0.0, 72), // 72 hours
  monthly('Monthly', 3.99, 30), // 30 days
  yearly('Yearly', 34.99, 365); // 365 days

  const SubscriptionType(this.displayName, this.price, this.durationInDays);

  final String displayName;
  final double price;
  final int durationInDays;

  /// Get duration in hours (for trial)
  int get durationInHours => durationInDays * 24;

  /// Get formatted price string
  String get formattedPrice {
    if (price == 0.0) return 'Free';
    return '\$${price.toStringAsFixed(2)}';
  }

  /// Get billing period description
  String get billingPeriod {
    switch (this) {
      case SubscriptionType.trial:
        return '72 hours';
      case SubscriptionType.monthly:
        return 'per month';
      case SubscriptionType.yearly:
        return 'per year';
    }
  }

  /// Get savings text for yearly subscription
  String? get savingsText {
    if (this == SubscriptionType.yearly) {
      final monthlyTotal = SubscriptionType.monthly.price * 12;
      final savings = monthlyTotal - price;
      final percentage = ((savings / monthlyTotal) * 100).round();
      return 'Save $percentage% (${savings.toStringAsFixed(2)})';
    }
    return null;
  }
}

/// Subscription status
enum SubscriptionStatus {
  active('Active'),
  inactive('Inactive'),
  expired('Expired'),
  cancelled('Cancelled'),
  pending('Pending'),
  failed('Failed');

  const SubscriptionStatus(this.displayName);

  final String displayName;

  /// Check if status allows access to features
  bool get allowsAccess => this == SubscriptionStatus.active;
}

/// Payment result model
class PaymentResult {
  final bool success;
  final String? orderId;
  final String? subscriptionId;
  final String? error;
  final Map<String, dynamic>? details;

  const PaymentResult({
    required this.success,
    this.orderId,
    this.subscriptionId,
    this.error,
    this.details,
  });

  /// Success result
  factory PaymentResult.success({
    String? orderId,
    String? subscriptionId,
    Map<String, dynamic>? details,
  }) {
    return PaymentResult(
      success: true,
      orderId: orderId,
      subscriptionId: subscriptionId,
      details: details,
    );
  }

  /// Error result
  factory PaymentResult.error(String error) {
    return PaymentResult(success: false, error: error);
  }
}
