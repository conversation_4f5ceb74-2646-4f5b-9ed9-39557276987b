import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:provider/provider.dart';
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/constants/app_constants.dart';
import 'core/config/subscription_config.dart';
import 'core/providers/auth_provider.dart';
import 'core/providers/base_auth_provider.dart';
import 'core/providers/subscription_provider.dart';

import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('Firebase initialized successfully');
  } catch (e) {
    debugPrint('Firebase initialization failed: $e');
    // App will continue with mock auth if Firebase fails
  }

  // Initialize Stripe
  try {
    Stripe.publishableKey = SubscriptionConfig.publishableKey;
    await Stripe.instance.applySettings();
    debugPrint('Stripe initialized successfully');
  } catch (e) {
    debugPrint('Stripe initialization failed: $e');
  }

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: AppConstants.primaryBlue,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  runApp(const SiraApp());
}

class SiraApp extends StatelessWidget {
  const SiraApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<BaseAuthProvider>(
          create: (context) => AuthProvider()..initialize(),
        ),
        ChangeNotifierProvider<SubscriptionProvider>(
          create: (context) => SubscriptionProvider(),
        ),
      ],
      child: MaterialApp.router(
        title: AppConstants.appFullName,
        debugShowCheckedModeBanner: false,

        // Theme configuration - Light theme only
        theme: AppTheme.lightTheme,

        // Router configuration
        routerConfig: AppRouter.router,
      ),
    );
  }
}
