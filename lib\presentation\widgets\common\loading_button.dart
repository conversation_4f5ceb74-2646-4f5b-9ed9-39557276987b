import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

/// Custom loading button widget
class LoadingButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;
  final String? text;
  final Widget? child;
  final bool isOutlined;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double height;

  const LoadingButton({
    super.key,
    required this.onPressed,
    required this.isLoading,
    this.text,
    this.child,
    this.isOutlined = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height = AppConstants.buttonHeightM,
  }) : assert(
         text != null || child != null,
         'Either text or child must be provided',
       );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isOutlined) {
      return SizedBox(
        width: width ?? double.infinity,
        height: height,
        child: OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: textColor ?? AppConstants.primaryBlue,
            side: BorderSide(
              color: backgroundColor ?? AppConstants.primaryBlue,
              width: 1.5,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusM),
            ),
          ),
          child: _buildButtonContent(theme),
        ),
      );
    }

    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppConstants.primaryBlue,
          foregroundColor: textColor ?? AppConstants.white,
          disabledBackgroundColor: AppConstants.mediumGray.withOpacity(0.3),
          disabledForegroundColor: AppConstants.mediumGray,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
          ),
        ),
        child: _buildButtonContent(theme),
      ),
    );
  }

  Widget _buildButtonContent(ThemeData theme) {
    if (isLoading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                textColor ?? AppConstants.white,
              ),
            ),
          ),
          const SizedBox(width: AppConstants.spacingS),
          Text(
            'Loading...',
            style: theme.textTheme.labelLarge?.copyWith(
              color: textColor ?? AppConstants.white,
            ),
          ),
        ],
      );
    }

    // Use child if provided, otherwise use text
    if (child != null) {
      return child!;
    }

    if (icon != null && text != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20, color: textColor ?? AppConstants.white),
          const SizedBox(width: AppConstants.spacingS),
          Text(
            text!,
            style: theme.textTheme.labelLarge?.copyWith(
              color: textColor ?? AppConstants.white,
            ),
          ),
        ],
      );
    }

    return Text(
      text ?? '',
      style: theme.textTheme.labelLarge?.copyWith(
        color: textColor ?? AppConstants.white,
      ),
    );
  }
}
