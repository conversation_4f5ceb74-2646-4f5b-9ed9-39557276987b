import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';

/// Terms of Service screen
class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Service'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.spacingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              Text(
                'Terms of Service',
                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                  color: AppConstants.primaryPink,
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                ),
              ),

              const SizedBox(height: AppConstants.spacingXL),

              // Content
              Text(
                'Welcome to the Smart Invoice-Receipt Assistant (the "Service"). These Terms of Service ("Terms") govern your use of our App (SIRA) and any related services provided. By accessing or using the Service, you agree to be bound by these Terms.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  height: 1.6,
                ),
              ),

              const SizedBox(height: AppConstants.spacingL),

              _buildSection(
                context,
                '1. Description of Service',
                'The Smart Invoice-Receipt Assistant is a web-based application that utilizes artificial intelligence (AI) to assist users in creating, managing, and generating professional invoices and receipts. Key features include AI-powered auto-filling of business and customer details, intelligent line item suggestions, automated calculations, and the ability to generate PDFs.',
              ),

              _buildSection(
                context,
                '2. User Accounts & Responsibilities',
                '• You are responsible for the accuracy of all information you provide to the Service, including business details, customer information, line items, and pricing. The Service provides AI-powered suggestions, but you are ultimately responsible for reviewing and confirming all data before generating a final document.\n• You are responsible for maintaining the confidentiality of any account information and for all activities that occur under your account.',
              ),

              _buildSection(
                context,
                '3. Use of the Service',
                'You agree to use the Service only for lawful purposes. You will not use the Service:\n• To create fraudulent or misleading documents.\n• To upload or transmit any material that is illegal, defamatory, or infringes on the rights of any third party.\n• To attempt to disrupt or interfere with the Service\'s servers or networks.',
              ),

              _buildSection(
                context,
                '4. AI-Powered Features',
                '• Our Service uses AI to provide suggestions and auto-fill information. While we strive for accuracy, these AI-generated outputs may sometimes be incorrect, incomplete, or based on publicly available data that may not be up-to-date.\n• You must independently verify the accuracy of all AI-generated content before relying on it for financial or legal purposes. We are not liable for any errors or omissions in the AI-generated content.',
              ),

              _buildSection(
                context,
                '5. Intellectual Property',
                '• Your Data: You retain all ownership rights to the content and data you input into the Service (e.g., your customer lists, item descriptions, and generated invoices).\n• Our Service: We own all rights, title, and interest in and to the Service itself, including its software, design, branding, and the underlying technology. You are granted a limited, non-exclusive, revocable license to use the Service in accordance with these Terms.',
              ),

              _buildSection(
                context,
                '6. Data Privacy',
                'Our collection and use of personal information in connection with the Service are described in our Privacy Policy. By using the Service, you agree to the terms of the Privacy Policy.',
              ),

              _buildSection(
                context,
                '7. Disclaimers and Limitation of Liability',
                '• THE SERVICE IS PROVIDED "AS IS" AND "AS AVAILABLE," WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED. WE DO NOT WARRANT THAT THE SERVICE WILL BE UNINTERRUPTED, ERROR-FREE, OR COMPLETELY SECURE.\n• TO THE FULLEST EXTENT PERMITTED BY LAW, IN NO EVENT WILL WE, OUR AFFILIATES, OR OUR LICENSORS BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, OR ANY LOSS OF PROFITS OR REVENUES, WHETHER INCURRED DIRECTLY OR INDIRECTLY, OR ANY LOSS OF DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM (a) YOUR USE OF OR INABILITY TO USE THE SERVICE; (b) ANY ERRORS OR INACCURACIES IN THE CONTENT, INCLUDING CALCULATIONS OR AI-GENERATED INFORMATION; (c) ANY UNAUTHORIZED ACCESS TO OR USE OF OUR SERVERS.',
              ),

              _buildSection(
                context,
                '8. Termination',
                'We may terminate or suspend your access to the Service at any time, with or without cause or notice, for conduct that we believe violates these Terms or is harmful to other users of the Service, us, or third parties.',
              ),

              _buildSection(
                context,
                '9. Changes to Terms',
                'We reserve the right to modify these Terms at any time. We will provide notice of any significant changes. Your continued use of the Service after such changes constitutes your acceptance of the new Terms.',
              ),

              _buildSection(
                context,
                '10. Governing Law',
                'These Terms shall be governed by the laws of the UK without regard to its conflict of law provisions.',
              ),

              _buildSection(
                context,
                'Contact Us',
                'If you have any questions about these Terms, please contact <NAME_EMAIL>',
              ),

              const SizedBox(height: AppConstants.spacingXXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryBlue,
          ),
        ),
        const SizedBox(height: AppConstants.spacingS),
        Text(
          content,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            height: 1.6,
          ),
        ),
        const SizedBox(height: AppConstants.spacingL),
      ],
    );
  }
}
